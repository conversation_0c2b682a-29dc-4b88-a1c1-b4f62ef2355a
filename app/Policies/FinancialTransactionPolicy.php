<?php

namespace App\Policies;

use App\Models\FinancialTransaction;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class FinancialTransactionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'manager']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, FinancialTransaction $financialTransaction): bool
    {
        return $user->hasAnyRole(['admin', 'manager']) ||
               $financialTransaction->user_id === $user->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'manager', 'operator']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, FinancialTransaction $financialTransaction): bool
    {
        return $user->hasAnyRole(['admin', 'manager']) ||
               ($financialTransaction->user_id === $user->id && $financialTransaction->status === 'pending');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, FinancialTransaction $financialTransaction): bool
    {
        return $user->hasRole('admin') ||
               ($user->hasRole('manager') && $financialTransaction->status === 'pending');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, FinancialTransaction $financialTransaction): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, FinancialTransaction $financialTransaction): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can approve transactions.
     */
    public function approve(User $user, FinancialTransaction $financialTransaction): bool
    {
        return $user->hasAnyRole(['admin', 'manager']) &&
               $financialTransaction->status === 'pending';
    }

    /**
     * Determine whether the user can view financial reports.
     */
    public function viewReports(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'manager']);
    }
}
