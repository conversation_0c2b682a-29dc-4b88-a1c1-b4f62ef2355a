<?php

namespace App\Http\Controllers;

use App\Models\Operation;
use App\Models\Location;
use App\Models\Asset;
use App\Models\CustomField;
use App\Models\CustomFieldValue;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;

class OperationController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Operation::class);

        $query = Operation::with(['location', 'asset', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('operation_code', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%");
            });
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by asset
        if ($request->filled('asset_id')) {
            $query->where('asset_id', $request->asset_id);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('scheduled_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('scheduled_at', '<=', $request->date_to);
        }

        $operations = $query->latest('scheduled_at')->paginate(20)->withQueryString();

        // Get filter options
        $locations = Location::active()->get();
        $assets = Asset::active()->get();

        // Get operation types
        $operationTypes = Operation::distinct()->pluck('type')->filter();

        return view('operations.index', compact('operations', 'locations', 'assets', 'operationTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Operation::class);

        $locations = Location::active()->get();
        $assets = Asset::active()->get();
        $customFields = CustomField::active()->forEntity('operation')->orderBy('sort_order')->get();

        return view('operations.create', compact('locations', 'assets', 'customFields'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Operation::class);

        $rules = [
            'location_id' => 'required|exists:locations,id',
            'asset_id' => 'nullable|exists:assets,id',
            'type' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'scheduled_at' => 'nullable|date',
            'estimated_duration' => 'nullable|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('operation')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Generate operation code
        $operationCode = Operation::generateOperationCode($validated['type']);

        // Create operation
        $operation = Operation::create([
            'location_id' => $validated['location_id'],
            'asset_id' => $validated['asset_id'],
            'user_id' => Auth::id(),
            'operation_code' => $operationCode,
            'type' => $validated['type'],
            'title' => $validated['title'],
            'description' => $validated['description'],
            'priority' => $validated['priority'],
            'status' => $validated['status'],
            'scheduled_at' => $validated['scheduled_at'],
            'estimated_duration' => $validated['estimated_duration'],
            'cost' => $validated['cost'],
            'notes' => $validated['notes'],
        ]);

        // Save custom field values
        if ($request->filled('custom_fields')) {
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $operation->id,
                        'entity_type' => 'operation',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('operations.index')
            ->with('success', 'Operation created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Operation $operation)
    {
        $this->authorize('view', $operation);

        $operation->load(['location', 'asset', 'user', 'customFieldValues.customField']);

        return view('operations.show', compact('operation'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Operation $operation)
    {
        $this->authorize('update', $operation);

        $operation->load(['location', 'asset', 'customFieldValues.customField']);
        $locations = Location::active()->get();
        $assets = Asset::active()->get();
        $customFields = CustomField::active()->forEntity('operation')->orderBy('sort_order')->get();

        return view('operations.edit', compact('operation', 'locations', 'assets', 'customFields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Operation $operation)
    {
        $this->authorize('update', $operation);

        $rules = [
            'location_id' => 'required|exists:locations,id',
            'asset_id' => 'nullable|exists:assets,id',
            'type' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'scheduled_at' => 'nullable|date',
            'estimated_duration' => 'nullable|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('operation')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        $operation->update([
            'location_id' => $validated['location_id'],
            'asset_id' => $validated['asset_id'],
            'type' => $validated['type'],
            'title' => $validated['title'],
            'description' => $validated['description'],
            'priority' => $validated['priority'],
            'status' => $validated['status'],
            'scheduled_at' => $validated['scheduled_at'],
            'estimated_duration' => $validated['estimated_duration'],
            'cost' => $validated['cost'],
            'notes' => $validated['notes'],
        ]);

        // Update custom field values
        if ($request->filled('custom_fields')) {
            // Delete existing custom field values
            $operation->customFieldValues()->delete();

            // Create new custom field values
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $operation->id,
                        'entity_type' => 'operation',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('operations.index')
            ->with('success', 'Operation updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Operation $operation)
    {
        $this->authorize('delete', $operation);

        // Delete custom field values
        $operation->customFieldValues()->delete();

        $operation->delete();

        return redirect()->route('operations.index')
            ->with('success', 'Operation deleted successfully.');
    }

    /**
     * Start an operation
     */
    public function start(Operation $operation)
    {
        $this->authorize('performOperation', $operation);

        $operation->start();

        return back()->with('success', 'Operation started successfully.');
    }

    /**
     * Complete an operation
     */
    public function complete(Request $request, Operation $operation)
    {
        $this->authorize('performOperation', $operation);

        $validated = $request->validate([
            'notes' => 'nullable|string',
        ]);

        $operation->complete($validated['notes'] ?? null);

        return back()->with('success', 'Operation completed successfully.');
    }
}
