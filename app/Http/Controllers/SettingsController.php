<?php

namespace App\Http\Controllers;

use App\Models\AppSetting;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display application settings
     */
    public function index()
    {
        // Only admin users can access settings
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $settings = AppSetting::orderBy('group')->orderBy('key')->get()->groupBy('group');

        return view('settings.index', compact('settings'));
    }

    /**
     * Update application settings
     */
    public function update(Request $request)
    {
        // Only admin users can update settings
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable|string',
        ]);

        foreach ($validated['settings'] as $key => $value) {
            $setting = AppSetting::where('key', $key)->first();
            if ($setting) {
                // Type casting based on setting type
                $typedValue = match($setting->type) {
                    'boolean' => $value === '1' || $value === 'true',
                    'integer' => (int) $value,
                    'json' => is_array($value) ? json_encode($value) : $value,
                    default => $value
                };

                $setting->update(['value' => $typedValue]);
            }
        }

        // Clear settings cache
        AppSetting::clearCache();

        return redirect()->route('settings.index')
            ->with('success', 'Settings updated successfully.');
    }

    /**
     * Reset settings to defaults
     */
    public function reset(Request $request)
    {
        // Only admin users can reset settings
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $validated = $request->validate([
            'group' => 'required|string',
        ]);

        // Reset settings for the specified group
        $defaultSettings = $this->getDefaultSettings();

        if (isset($defaultSettings[$validated['group']])) {
            foreach ($defaultSettings[$validated['group']] as $setting) {
                AppSetting::updateOrCreate(
                    ['key' => $setting['key']],
                    $setting
                );
            }
        }

        // Clear settings cache
        AppSetting::clearCache();

        return redirect()->route('settings.index')
            ->with('success', "Settings for {$validated['group']} group reset to defaults.");
    }

    /**
     * Get default settings configuration
     */
    private function getDefaultSettings()
    {
        return [
            'general' => [
                [
                    'key' => 'app_name',
                    'value' => 'VendApp - Vending Machine Management',
                    'type' => 'string',
                    'group' => 'general',
                    'label' => 'Application Name',
                    'description' => 'The name of your application',
                    'is_public' => true,
                ],
                [
                    'key' => 'currency',
                    'value' => 'USD',
                    'type' => 'string',
                    'group' => 'general',
                    'label' => 'Default Currency',
                    'description' => 'Default currency for financial transactions',
                    'is_public' => true,
                ],
                [
                    'key' => 'date_format',
                    'value' => 'Y-m-d',
                    'type' => 'string',
                    'group' => 'general',
                    'label' => 'Date Format',
                    'description' => 'Default date format for the application',
                    'is_public' => true,
                ],
                [
                    'key' => 'timezone',
                    'value' => 'America/New_York',
                    'type' => 'string',
                    'group' => 'general',
                    'label' => 'Timezone',
                    'description' => 'Default timezone for the application',
                    'is_public' => true,
                ],
            ],
            'financial' => [
                [
                    'key' => 'low_cash_threshold',
                    'value' => '100',
                    'type' => 'integer',
                    'group' => 'financial',
                    'label' => 'Low Cash Alert Threshold',
                    'description' => 'Alert when cash amount falls below this value',
                    'is_public' => false,
                ],
                [
                    'key' => 'auto_approve_transactions',
                    'value' => 'false',
                    'type' => 'boolean',
                    'group' => 'financial',
                    'label' => 'Auto-approve Transactions',
                    'description' => 'Automatically approve financial transactions',
                    'is_public' => false,
                ],
            ],
            'operations' => [
                [
                    'key' => 'maintenance_reminder_days',
                    'value' => '30',
                    'type' => 'integer',
                    'group' => 'operations',
                    'label' => 'Maintenance Reminder (Days)',
                    'description' => 'Send maintenance reminders this many days in advance',
                    'is_public' => false,
                ],
                [
                    'key' => 'auto_assign_operations',
                    'value' => 'false',
                    'type' => 'boolean',
                    'group' => 'operations',
                    'label' => 'Auto-assign Operations',
                    'description' => 'Automatically assign operations to available operators',
                    'is_public' => false,
                ],
            ],
        ];
    }
}
