<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\CustomField;
use App\Models\CustomFieldValue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,manager')->except(['show', 'index']);
        $this->middleware('role:admin')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', User::class);

        $query = User::with('roles');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->role($request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        $users = $query->paginate(15)->withQueryString();
        $roles = Role::all();
        $departments = User::distinct()->pluck('department')->filter();

        return view('users.index', compact('users', 'roles', 'departments'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', User::class);

        $roles = Role::all();
        $customFields = CustomField::active()->forEntity('user')->orderBy('sort_order')->get();

        return view('users.create', compact('roles', 'customFields'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', User::class);

        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'employee_id' => 'nullable|string|max:255|unique:users',
            'phone_number' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'status' => 'required|in:active,inactive,suspended',
            'bio' => 'nullable|string|max:1000',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,name',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('user')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $validated['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        // Create user
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'employee_id' => $validated['employee_id'],
            'phone_number' => $validated['phone_number'],
            'department' => $validated['department'],
            'position' => $validated['position'],
            'hire_date' => $validated['hire_date'],
            'status' => $validated['status'],
            'bio' => $validated['bio'],
            'avatar' => $validated['avatar'] ?? null,
        ]);

        // Assign roles
        $user->assignRole($validated['roles']);

        // Save custom field values
        if ($request->filled('custom_fields')) {
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $user->id,
                        'entity_type' => 'user',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $this->authorize('view', $user);

        $user->load('roles', 'customFieldValues.customField');

        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $this->authorize('update', $user);

        $user->load('roles', 'customFieldValues.customField');
        $roles = Role::all();
        $customFields = CustomField::active()->forEntity('user')->orderBy('sort_order')->get();

        return view('users.edit', compact('user', 'roles', 'customFields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $this->authorize('update', $user);

        $rules = [
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'employee_id' => ['nullable', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone_number' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'status' => 'required|in:active,inactive,suspended',
            'bio' => 'nullable|string|max:1000',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,name',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('user')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            $validated['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        // Update user
        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'employee_id' => $validated['employee_id'],
            'phone_number' => $validated['phone_number'],
            'department' => $validated['department'],
            'position' => $validated['position'],
            'hire_date' => $validated['hire_date'],
            'status' => $validated['status'],
            'bio' => $validated['bio'],
        ];

        if (isset($validated['avatar'])) {
            $updateData['avatar'] = $validated['avatar'];
        }

        if ($validated['password']) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        $user->update($updateData);

        // Update roles
        $user->syncRoles($validated['roles']);

        // Update custom field values
        if ($request->filled('custom_fields')) {
            // Delete existing custom field values
            $user->customFieldValues()->delete();

            // Create new custom field values
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $user->id,
                        'entity_type' => 'user',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $this->authorize('delete', $user);

        // Delete avatar
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        // Delete custom field values
        $user->customFieldValues()->delete();

        $user->delete();

        return redirect()->route('users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status
     */
    public function toggleStatus(User $user)
    {
        $this->authorize('update', $user);

        $newStatus = $user->status === 'active' ? 'inactive' : 'active';
        $user->update(['status' => $newStatus]);

        return back()->with('success', "User status changed to {$newStatus}.");
    }
}
