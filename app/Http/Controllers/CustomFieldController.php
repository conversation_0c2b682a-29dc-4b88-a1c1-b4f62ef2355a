<?php

namespace App\Http\Controllers;

use App\Models\CustomField;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;

class CustomFieldController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Only admin users can manage custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $query = CustomField::query();

        // Filter by entity type
        if ($request->filled('entity_type')) {
            $query->where('entity_type', $request->entity_type);
        }

        // Search by name or label
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('label', 'like', "%{$search}%");
            });
        }

        $customFields = $query->orderBy('entity_type')->orderBy('sort_order')->paginate(20)->withQueryString();

        $entityTypes = ['user', 'location', 'asset', 'operation', 'financial_transaction'];

        return view('custom-fields.index', compact('customFields', 'entityTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Only admin users can create custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $entityTypes = ['user', 'location', 'asset', 'operation', 'financial_transaction'];
        $fieldTypes = ['text', 'textarea', 'number', 'decimal', 'date', 'datetime', 'boolean', 'select', 'multiselect', 'file'];

        return view('custom-fields.create', compact('entityTypes', 'fieldTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Only admin users can create custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|regex:/^[a-z_]+$/|unique:custom_fields,name',
            'label' => 'required|string|max:255',
            'entity_type' => 'required|in:user,location,asset,operation,financial_transaction',
            'field_type' => 'required|in:text,textarea,number,decimal,date,datetime,boolean,select,multiselect,file',
            'options' => 'nullable|array',
            'options.*' => 'nullable|string',
            'default_value' => 'nullable|string',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'help_text' => 'nullable|string|max:500',
            'validation_rules' => 'nullable|array',
            'validation_rules.*' => 'nullable|string',
        ]);

        // Process options for select/multiselect fields
        if (in_array($validated['field_type'], ['select', 'multiselect']) && $request->filled('options')) {
            $options = [];
            foreach ($request->options as $key => $value) {
                if (!empty($value)) {
                    $options[$key] = $value;
                }
            }
            $validated['options'] = $options;
        }

        // Set default sort order
        if (!isset($validated['sort_order'])) {
            $maxOrder = CustomField::where('entity_type', $validated['entity_type'])->max('sort_order') ?? 0;
            $validated['sort_order'] = $maxOrder + 1;
        }

        CustomField::create($validated);

        return redirect()->route('custom-fields.index')
            ->with('success', 'Custom field created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(CustomField $customField)
    {
        // Only admin users can view custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        return view('custom-fields.show', compact('customField'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CustomField $customField)
    {
        // Only admin users can edit custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $entityTypes = ['user', 'location', 'asset', 'operation', 'financial_transaction'];
        $fieldTypes = ['text', 'textarea', 'number', 'decimal', 'date', 'datetime', 'boolean', 'select', 'multiselect', 'file'];

        return view('custom-fields.edit', compact('customField', 'entityTypes', 'fieldTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CustomField $customField)
    {
        // Only admin users can update custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|regex:/^[a-z_]+$/|unique:custom_fields,name,' . $customField->id,
            'label' => 'required|string|max:255',
            'entity_type' => 'required|in:user,location,asset,operation,financial_transaction',
            'field_type' => 'required|in:text,textarea,number,decimal,date,datetime,boolean,select,multiselect,file',
            'options' => 'nullable|array',
            'options.*' => 'nullable|string',
            'default_value' => 'nullable|string',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'help_text' => 'nullable|string|max:500',
            'validation_rules' => 'nullable|array',
            'validation_rules.*' => 'nullable|string',
        ]);

        // Process options for select/multiselect fields
        if (in_array($validated['field_type'], ['select', 'multiselect']) && $request->filled('options')) {
            $options = [];
            foreach ($request->options as $key => $value) {
                if (!empty($value)) {
                    $options[$key] = $value;
                }
            }
            $validated['options'] = $options;
        }

        $customField->update($validated);

        return redirect()->route('custom-fields.index')
            ->with('success', 'Custom field updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CustomField $customField)
    {
        // Only admin users can delete custom fields
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        // Delete all associated custom field values
        $customField->values()->delete();

        $customField->delete();

        return redirect()->route('custom-fields.index')
            ->with('success', 'Custom field deleted successfully.');
    }
}
