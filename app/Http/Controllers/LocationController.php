<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\CustomField;
use App\Models\CustomFieldValue;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class LocationController extends Controller
{
    use AuthorizesRequests;

    // Middleware is handled in routes/web.php

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Location::class);

        $query = Location::withCount(['assets', 'operations']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $locations = $query->paginate(15)->withQueryString();

        return view('locations.index', compact('locations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Location::class);

        $customFields = CustomField::active()->forEntity('location')->orderBy('sort_order')->get();

        return view('locations.create', compact('customFields'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Location::class);

        $rules = [
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'phone_number' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_email' => 'nullable|email|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive,maintenance',
            'operating_hours' => 'nullable|array',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('location')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Create location
        $location = Location::create([
            'name' => $validated['name'],
            'address' => $validated['address'],
            'latitude' => $validated['latitude'],
            'longitude' => $validated['longitude'],
            'phone_number' => $validated['phone_number'],
            'contact_person' => $validated['contact_person'],
            'contact_email' => $validated['contact_email'],
            'description' => $validated['description'],
            'status' => $validated['status'],
            'operating_hours' => $validated['operating_hours'],
        ]);

        // Save custom field values
        if ($request->filled('custom_fields')) {
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $location->id,
                        'entity_type' => 'location',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('locations.index')
            ->with('success', 'Location created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Location $location)
    {
        $this->authorize('view', $location);

        $location->load([
            'assets' => function ($query) {
                $query->with('operations')->latest();
            },
            'operations' => function ($query) {
                $query->with('user')->latest()->limit(10);
            },
            'financialTransactions' => function ($query) {
                $query->with('user')->latest()->limit(10);
            },
            'customFieldValues.customField'
        ]);

        // Calculate financial summary
        $financialSummary = [
            'total_revenue' => $location->total_revenue,
            'total_expenses' => $location->total_expenses,
            'profit' => $location->profit,
            'monthly_revenue' => $location->financialTransactions()
                ->where('type', 'revenue')
                ->where('status', 'completed')
                ->whereMonth('transaction_date', now()->month)
                ->whereYear('transaction_date', now()->year)
                ->sum('amount'),
            'monthly_expenses' => $location->financialTransactions()
                ->whereIn('type', ['expense', 'maintenance'])
                ->where('status', 'completed')
                ->whereMonth('transaction_date', now()->month)
                ->whereYear('transaction_date', now()->year)
                ->sum('amount'),
        ];
        $financialSummary['monthly_profit'] = $financialSummary['monthly_revenue'] - $financialSummary['monthly_expenses'];

        return view('locations.show', compact('location', 'financialSummary'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Location $location)
    {
        $this->authorize('update', $location);

        $location->load('customFieldValues.customField');
        $customFields = CustomField::active()->forEntity('location')->orderBy('sort_order')->get();

        return view('locations.edit', compact('location', 'customFields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Location $location)
    {
        $this->authorize('update', $location);

        $rules = [
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'phone_number' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_email' => 'nullable|email|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive,maintenance',
            'operating_hours' => 'nullable|array',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('location')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Update location
        $location->update([
            'name' => $validated['name'],
            'address' => $validated['address'],
            'latitude' => $validated['latitude'],
            'longitude' => $validated['longitude'],
            'phone_number' => $validated['phone_number'],
            'contact_person' => $validated['contact_person'],
            'contact_email' => $validated['contact_email'],
            'description' => $validated['description'],
            'status' => $validated['status'],
            'operating_hours' => $validated['operating_hours'],
        ]);

        // Update custom field values
        if ($request->filled('custom_fields')) {
            // Delete existing custom field values
            $location->customFieldValues()->delete();

            // Create new custom field values
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $location->id,
                        'entity_type' => 'location',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('locations.index')
            ->with('success', 'Location updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Location $location)
    {
        $this->authorize('delete', $location);

        // Check if location has assets
        if ($location->assets()->count() > 0) {
            return back()->with('error', 'Cannot delete location with existing assets.');
        }

        // Delete custom field values
        $location->customFieldValues()->delete();

        $location->delete();

        return redirect()->route('locations.index')
            ->with('success', 'Location deleted successfully.');
    }
}
