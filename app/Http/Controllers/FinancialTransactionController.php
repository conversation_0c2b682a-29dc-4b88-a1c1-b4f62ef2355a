<?php

namespace App\Http\Controllers;

use App\Models\FinancialTransaction;
use App\Models\Location;
use App\Models\Asset;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FinancialTransactionController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', FinancialTransaction::class);

        $query = FinancialTransaction::with(['location', 'asset', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by asset
        if ($request->filled('asset_id')) {
            $query->where('asset_id', $request->asset_id);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('transaction_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('transaction_date', '<=', $request->date_to);
        }

        $transactions = $query->latest('transaction_date')->paginate(20)->withQueryString();

        // Get filter options
        $locations = Location::active()->get();
        $assets = Asset::active()->get();

        // Calculate summary statistics
        $summary = [
            'total_revenue' => $query->clone()->where('type', 'revenue')->where('status', 'completed')->sum('amount'),
            'total_expenses' => $query->clone()->whereIn('type', ['expense', 'maintenance'])->where('status', 'completed')->sum('amount'),
            'pending_amount' => $query->clone()->where('status', 'pending')->sum('amount'),
            'transaction_count' => $query->clone()->count(),
        ];
        $summary['profit'] = $summary['total_revenue'] - $summary['total_expenses'];

        return view('financial-transactions.index', compact('transactions', 'locations', 'assets', 'summary'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', FinancialTransaction::class);

        $locations = Location::active()->get();
        $assets = Asset::active()->get();

        return view('financial-transactions.create', compact('locations', 'assets'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', FinancialTransaction::class);

        $validated = $request->validate([
            'location_id' => 'required|exists:locations,id',
            'asset_id' => 'nullable|exists:assets,id',
            'type' => 'required|in:revenue,expense,maintenance,refund,collection',
            'category' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'description' => 'nullable|string',
            'transaction_date' => 'required|date',
            'payment_method' => 'nullable|in:cash,card,bank_transfer,other',
            'reference_number' => 'nullable|string|max:255',
            'status' => 'required|in:pending,completed,cancelled',
        ]);

        // Generate transaction code
        $transactionCode = FinancialTransaction::generateTransactionCode($validated['type']);

        // Create transaction
        $transaction = FinancialTransaction::create([
            'location_id' => $validated['location_id'],
            'asset_id' => $validated['asset_id'],
            'user_id' => Auth::id(),
            'transaction_code' => $transactionCode,
            'type' => $validated['type'],
            'category' => $validated['category'],
            'amount' => $validated['amount'],
            'currency' => $validated['currency'],
            'description' => $validated['description'],
            'transaction_date' => $validated['transaction_date'],
            'payment_method' => $validated['payment_method'],
            'reference_number' => $validated['reference_number'],
            'status' => $validated['status'],
        ]);

        return redirect()->route('financial-transactions.index')
            ->with('success', 'Financial transaction created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(FinancialTransaction $financialTransaction)
    {
        $this->authorize('view', $financialTransaction);

        $financialTransaction->load(['location', 'asset', 'user']);

        return view('financial-transactions.show', compact('financialTransaction'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FinancialTransaction $financialTransaction)
    {
        $this->authorize('update', $financialTransaction);

        $financialTransaction->load(['location', 'asset']);
        $locations = Location::active()->get();
        $assets = Asset::active()->get();

        return view('financial-transactions.edit', compact('financialTransaction', 'locations', 'assets'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FinancialTransaction $financialTransaction)
    {
        $this->authorize('update', $financialTransaction);

        $validated = $request->validate([
            'location_id' => 'required|exists:locations,id',
            'asset_id' => 'nullable|exists:assets,id',
            'type' => 'required|in:revenue,expense,maintenance,refund,collection',
            'category' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'description' => 'nullable|string',
            'transaction_date' => 'required|date',
            'payment_method' => 'nullable|in:cash,card,bank_transfer,other',
            'reference_number' => 'nullable|string|max:255',
            'status' => 'required|in:pending,completed,cancelled',
        ]);

        $financialTransaction->update($validated);

        return redirect()->route('financial-transactions.index')
            ->with('success', 'Financial transaction updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FinancialTransaction $financialTransaction)
    {
        $this->authorize('delete', $financialTransaction);

        $financialTransaction->delete();

        return redirect()->route('financial-transactions.index')
            ->with('success', 'Financial transaction deleted successfully.');
    }

    /**
     * Display financial reports
     */
    public function reports(Request $request)
    {
        $this->authorize('viewReports', FinancialTransaction::class);

        // Date range for reports (default to current month)
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        // Revenue vs Expenses over time
        $dailyData = FinancialTransaction::select(
                'transaction_date',
                DB::raw('SUM(CASE WHEN type = "revenue" AND status = "completed" THEN amount ELSE 0 END) as revenue'),
                DB::raw('SUM(CASE WHEN type IN ("expense", "maintenance") AND status = "completed" THEN amount ELSE 0 END) as expenses')
            )
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->groupBy('transaction_date')
            ->orderBy('transaction_date')
            ->get();

        // Location performance
        $locationPerformance = Location::select('locations.*')
            ->withSum(['financialTransactions as revenue' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'revenue')
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->withSum(['financialTransactions as expenses' => function ($query) use ($startDate, $endDate) {
                $query->whereIn('type', ['expense', 'maintenance'])
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->get()
            ->map(function ($location) {
                $location->profit = ($location->revenue ?? 0) - ($location->expenses ?? 0);
                return $location;
            })
            ->sortByDesc('profit');

        // Asset performance
        $assetPerformance = Asset::select('assets.*')
            ->withSum(['financialTransactions as revenue' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'revenue')
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->withSum(['financialTransactions as maintenance_cost' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'maintenance')
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->get()
            ->map(function ($asset) {
                $asset->profit = ($asset->revenue ?? 0) - ($asset->maintenance_cost ?? 0);
                return $asset;
            })
            ->sortByDesc('profit');

        // Summary statistics
        $summary = [
            'total_revenue' => FinancialTransaction::where('type', 'revenue')
                ->where('status', 'completed')
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->sum('amount'),
            'total_expenses' => FinancialTransaction::whereIn('type', ['expense', 'maintenance'])
                ->where('status', 'completed')
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->sum('amount'),
            'transaction_count' => FinancialTransaction::whereBetween('transaction_date', [$startDate, $endDate])->count(),
            'avg_transaction' => FinancialTransaction::whereBetween('transaction_date', [$startDate, $endDate])->avg('amount'),
        ];
        $summary['profit'] = $summary['total_revenue'] - $summary['total_expenses'];

        return view('financial-transactions.reports', compact(
            'dailyData',
            'locationPerformance',
            'assetPerformance',
            'summary',
            'startDate',
            'endDate'
        ));
    }
}
