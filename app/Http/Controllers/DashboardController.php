<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Location;
use App\Models\Asset;
use App\Models\FinancialTransaction;
use App\Models\Operation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    // Middleware is handled in routes/web.php

    /**
     * Display the dashboard
     */
    public function index()
    {
        $user = Auth::user();

        // Basic statistics
        $stats = [
            'total_locations' => Location::count(),
            'active_locations' => Location::where('status', 'active')->count(),
            'total_assets' => Asset::count(),
            'active_assets' => Asset::where('status', 'active')->count(),
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
        ];

        // Financial data (only for admin/manager)
        $financialData = [];
        if ($user->hasAnyRole(['admin', 'manager'])) {
            $financialData = [
                'total_revenue' => FinancialTransaction::where('type', 'revenue')
                    ->where('status', 'completed')
                    ->sum('amount'),
                'total_expenses' => FinancialTransaction::whereIn('type', ['expense', 'maintenance'])
                    ->where('status', 'completed')
                    ->sum('amount'),
                'monthly_revenue' => FinancialTransaction::where('type', 'revenue')
                    ->where('status', 'completed')
                    ->whereMonth('transaction_date', now()->month)
                    ->whereYear('transaction_date', now()->year)
                    ->sum('amount'),
                'monthly_expenses' => FinancialTransaction::whereIn('type', ['expense', 'maintenance'])
                    ->where('status', 'completed')
                    ->whereMonth('transaction_date', now()->month)
                    ->whereYear('transaction_date', now()->year)
                    ->sum('amount'),
            ];
            $financialData['profit'] = $financialData['total_revenue'] - $financialData['total_expenses'];
            $financialData['monthly_profit'] = $financialData['monthly_revenue'] - $financialData['monthly_expenses'];
        }

        // Recent operations
        $recentOperations = Operation::with(['location', 'asset', 'user'])
            ->latest()
            ->limit(5)
            ->get();

        // Pending operations
        $pendingOperations = Operation::with(['location', 'asset'])
            ->where('status', 'pending')
            ->orderBy('priority', 'desc')
            ->orderBy('scheduled_at')
            ->limit(10)
            ->get();

        // High priority operations
        $urgentOperations = Operation::with(['location', 'asset'])
            ->whereIn('priority', ['high', 'urgent'])
            ->whereIn('status', ['pending', 'in_progress'])
            ->orderBy('priority', 'desc')
            ->orderBy('scheduled_at')
            ->limit(5)
            ->get();

        // Recent financial transactions (only for admin/manager)
        $recentTransactions = [];
        if ($user->hasAnyRole(['admin', 'manager'])) {
            $recentTransactions = FinancialTransaction::with(['location', 'asset', 'user'])
                ->latest()
                ->limit(5)
                ->get();
        }

        // Asset status distribution
        $assetStatusData = Asset::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Location performance (only for admin/manager)
        $locationPerformance = [];
        if ($user->hasAnyRole(['admin', 'manager'])) {
            $locationPerformance = Location::with(['financialTransactions' => function ($query) {
                $query->where('status', 'completed')
                      ->whereMonth('transaction_date', now()->month)
                      ->whereYear('transaction_date', now()->year);
            }])
            ->get()
            ->map(function ($location) {
                $revenue = $location->financialTransactions
                    ->where('type', 'revenue')
                    ->sum('amount');
                $expenses = $location->financialTransactions
                    ->whereIn('type', ['expense', 'maintenance'])
                    ->sum('amount');

                return [
                    'name' => $location->name,
                    'revenue' => $revenue,
                    'expenses' => $expenses,
                    'profit' => $revenue - $expenses,
                ];
            })
            ->sortByDesc('profit')
            ->take(5);
        }

        return view('dashboard', compact(
            'stats',
            'financialData',
            'recentOperations',
            'pendingOperations',
            'urgentOperations',
            'recentTransactions',
            'assetStatusData',
            'locationPerformance'
        ));
    }
}
