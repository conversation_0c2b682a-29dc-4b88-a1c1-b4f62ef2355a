<?php

namespace App\Http\Controllers;

use App\Models\Asset;
use App\Models\Location;
use App\Models\CustomField;
use App\Models\CustomFieldValue;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class AssetController extends Controller
{
    use AuthorizesRequests;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,manager')->except(['show', 'index']);
        $this->middleware('role:admin')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Asset::class);

        $query = Asset::with(['location', 'operations' => function ($q) {
            $q->latest()->limit(1);
        }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('asset_code', 'like', "%{$search}%")
                  ->orWhere('qr_code', 'like', "%{$search}%")
                  ->orWhere('serial_number', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%");
            });
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by condition
        if ($request->filled('condition')) {
            $query->where('condition', $request->condition);
        }

        $assets = $query->paginate(15)->withQueryString();
        $locations = Location::active()->get();

        return view('assets.index', compact('assets', 'locations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Asset::class);

        $locations = Location::active()->get();
        $customFields = CustomField::active()->forEntity('asset')->orderBy('sort_order')->get();

        return view('assets.create', compact('locations', 'customFields'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Asset::class);

        $rules = [
            'location_id' => 'required|exists:locations,id',
            'name' => 'required|string|max:255',
            'serial_number' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'purchase_price' => 'nullable|numeric|min:0',
            'current_value' => 'nullable|numeric|min:0',
            'purchase_date' => 'nullable|date',
            'warranty_expiry' => 'nullable|date',
            'status' => 'required|in:active,inactive,maintenance,retired',
            'condition' => 'required|in:excellent,good,fair,poor',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'specifications' => 'nullable|array',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('asset')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Generate asset code and QR code
        $assetCode = Asset::generateAssetCode($validated['location_id']);
        $qrCode = Asset::generateQrCode();

        // Handle image upload
        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('assets', 'public');
        }

        // Create asset
        $asset = Asset::create([
            'location_id' => $validated['location_id'],
            'name' => $validated['name'],
            'asset_code' => $assetCode,
            'qr_code' => $qrCode,
            'serial_number' => $validated['serial_number'],
            'model' => $validated['model'],
            'manufacturer' => $validated['manufacturer'],
            'description' => $validated['description'],
            'purchase_price' => $validated['purchase_price'],
            'current_value' => $validated['current_value'] ?? $validated['purchase_price'],
            'purchase_date' => $validated['purchase_date'],
            'warranty_expiry' => $validated['warranty_expiry'],
            'status' => $validated['status'],
            'condition' => $validated['condition'],
            'image_path' => $imagePath,
            'specifications' => $validated['specifications'],
        ]);

        // Save custom field values
        if ($request->filled('custom_fields')) {
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $asset->id,
                        'entity_type' => 'asset',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('assets.index')
            ->with('success', 'Asset created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Asset $asset)
    {
        $this->authorize('view', $asset);

        $asset->load([
            'location',
            'operations' => function ($query) {
                $query->with('user')->latest()->limit(10);
            },
            'financialTransactions' => function ($query) {
                $query->with('user')->latest()->limit(10);
            },
            'customFieldValues.customField'
        ]);

        // Calculate financial summary
        $financialSummary = [
            'total_revenue' => $asset->total_revenue,
            'maintenance_cost' => $asset->maintenance_cost,
            'profit' => $asset->total_revenue - $asset->maintenance_cost,
        ];

        return view('assets.show', compact('asset', 'financialSummary'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Asset $asset)
    {
        $this->authorize('update', $asset);

        $asset->load('customFieldValues.customField');
        $locations = Location::active()->get();
        $customFields = CustomField::active()->forEntity('asset')->orderBy('sort_order')->get();

        return view('assets.edit', compact('asset', 'locations', 'customFields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Asset $asset)
    {
        $this->authorize('update', $asset);

        $rules = [
            'location_id' => 'required|exists:locations,id',
            'name' => 'required|string|max:255',
            'serial_number' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'purchase_price' => 'nullable|numeric|min:0',
            'current_value' => 'nullable|numeric|min:0',
            'purchase_date' => 'nullable|date',
            'warranty_expiry' => 'nullable|date',
            'status' => 'required|in:active,inactive,maintenance,retired',
            'condition' => 'required|in:excellent,good,fair,poor',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'specifications' => 'nullable|array',
        ];

        // Add custom field validation rules
        $customFields = CustomField::active()->forEntity('asset')->get();
        foreach ($customFields as $field) {
            $fieldRules = $field->getValidationRulesArray();
            if (!empty($fieldRules)) {
                $rules["custom_fields.{$field->name}"] = $fieldRules;
            }
        }

        $validated = $request->validate($rules);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($asset->image_path) {
                Storage::disk('public')->delete($asset->image_path);
            }
            $validated['image_path'] = $request->file('image')->store('assets', 'public');
        }

        // Update asset
        $updateData = [
            'location_id' => $validated['location_id'],
            'name' => $validated['name'],
            'serial_number' => $validated['serial_number'],
            'model' => $validated['model'],
            'manufacturer' => $validated['manufacturer'],
            'description' => $validated['description'],
            'purchase_price' => $validated['purchase_price'],
            'current_value' => $validated['current_value'],
            'purchase_date' => $validated['purchase_date'],
            'warranty_expiry' => $validated['warranty_expiry'],
            'status' => $validated['status'],
            'condition' => $validated['condition'],
            'specifications' => $validated['specifications'],
        ];

        if (isset($validated['image_path'])) {
            $updateData['image_path'] = $validated['image_path'];
        }

        $asset->update($updateData);

        // Update custom field values
        if ($request->filled('custom_fields')) {
            // Delete existing custom field values
            $asset->customFieldValues()->delete();

            // Create new custom field values
            foreach ($request->custom_fields as $fieldName => $value) {
                $customField = $customFields->where('name', $fieldName)->first();
                if ($customField && $value !== null && $value !== '') {
                    CustomFieldValue::create([
                        'custom_field_id' => $customField->id,
                        'entity_id' => $asset->id,
                        'entity_type' => 'asset',
                        'value' => is_array($value) ? json_encode($value) : $value,
                    ]);
                }
            }
        }

        return redirect()->route('assets.index')
            ->with('success', 'Asset updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Asset $asset)
    {
        $this->authorize('delete', $asset);

        // Delete image
        if ($asset->image_path) {
            Storage::disk('public')->delete($asset->image_path);
        }

        // Delete custom field values
        $asset->customFieldValues()->delete();

        $asset->delete();

        return redirect()->route('assets.index')
            ->with('success', 'Asset deleted successfully.');
    }

    /**
     * Generate and display QR code for asset
     */
    public function generateQrCode(Asset $asset)
    {
        $this->authorize('view', $asset);

        $qrCode = QrCode::size(300)->generate($asset->qr_code);

        return view('assets.qr-code', compact('asset', 'qrCode'));
    }
}
