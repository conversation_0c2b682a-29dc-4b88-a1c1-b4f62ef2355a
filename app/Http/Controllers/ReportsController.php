<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Location;
use App\Models\Asset;
use App\Models\FinancialTransaction;
use App\Models\Operation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ReportsController extends Controller
{
    /**
     * Display the main reports dashboard
     */
    public function index()
    {
        // Check if user has permission to view reports
        if (!Auth::user()->hasAnyRole(['admin', 'manager'])) {
            abort(403, 'Access denied. Manager or Admin privileges required.');
        }

        // Get summary statistics for the dashboard
        $stats = [
            'total_locations' => Location::active()->count(),
            'total_assets' => Asset::active()->count(),
            'total_users' => User::count(),
            'pending_operations' => Operation::where('status', 'pending')->count(),
            'overdue_operations' => Operation::where('status', 'pending')
                ->where('scheduled_at', '<', now())
                ->count(),
            'monthly_revenue' => FinancialTransaction::where('type', 'revenue')
                ->where('status', 'completed')
                ->whereMonth('transaction_date', now()->month)
                ->whereYear('transaction_date', now()->year)
                ->sum('amount'),
            'monthly_expenses' => FinancialTransaction::whereIn('type', ['expense', 'maintenance'])
                ->where('status', 'completed')
                ->whereMonth('transaction_date', now()->month)
                ->whereYear('transaction_date', now()->year)
                ->sum('amount'),
        ];
        $stats['monthly_profit'] = $stats['monthly_revenue'] - $stats['monthly_expenses'];

        // Recent activity
        $recentTransactions = FinancialTransaction::with(['location', 'asset', 'user'])
            ->latest('transaction_date')
            ->take(5)
            ->get();

        $recentOperations = Operation::with(['location', 'asset', 'user'])
            ->latest('created_at')
            ->take(5)
            ->get();

        return view('reports.index', compact('stats', 'recentTransactions', 'recentOperations'));
    }

    /**
     * Asset performance report
     */
    public function assetPerformance(Request $request)
    {
        if (!Auth::user()->hasAnyRole(['admin', 'manager'])) {
            abort(403, 'Access denied. Manager or Admin privileges required.');
        }

        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $assets = Asset::with(['location'])
            ->withSum(['financialTransactions as revenue' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'revenue')
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->withSum(['financialTransactions as maintenance_cost' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'maintenance')
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->withCount(['operations as maintenance_count' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'maintenance')
                      ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
            }])
            ->get()
            ->map(function ($asset) {
                $asset->profit = ($asset->revenue ?? 0) - ($asset->maintenance_cost ?? 0);
                $asset->roi = $asset->maintenance_cost > 0 ?
                    (($asset->profit / $asset->maintenance_cost) * 100) : 0;
                return $asset;
            })
            ->sortByDesc('profit');

        return view('reports.asset-performance', compact('assets', 'startDate', 'endDate'));
    }

    /**
     * Location performance report
     */
    public function locationPerformance(Request $request)
    {
        if (!Auth::user()->hasAnyRole(['admin', 'manager'])) {
            abort(403, 'Access denied. Manager or Admin privileges required.');
        }

        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $locations = Location::withSum(['financialTransactions as revenue' => function ($query) use ($startDate, $endDate) {
                $query->where('type', 'revenue')
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->withSum(['financialTransactions as expenses' => function ($query) use ($startDate, $endDate) {
                $query->whereIn('type', ['expense', 'maintenance'])
                      ->where('status', 'completed')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
            }], 'amount')
            ->withCount(['assets as asset_count'])
            ->withCount(['operations as operation_count' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
            }])
            ->get()
            ->map(function ($location) {
                $location->profit = ($location->revenue ?? 0) - ($location->expenses ?? 0);
                $location->avg_revenue_per_asset = $location->asset_count > 0 ?
                    ($location->revenue ?? 0) / $location->asset_count : 0;
                return $location;
            })
            ->sortByDesc('profit');

        return view('reports.location-performance', compact('locations', 'startDate', 'endDate'));
    }

    /**
     * Operations report
     */
    public function operations(Request $request)
    {
        if (!Auth::user()->hasAnyRole(['admin', 'manager'])) {
            abort(403, 'Access denied. Manager or Admin privileges required.');
        }

        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        // Operations by status
        $operationsByStatus = Operation::select('status', DB::raw('count(*) as count'))
            ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->groupBy('status')
            ->get();

        // Operations by type
        $operationsByType = Operation::select('type', DB::raw('count(*) as count'))
            ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->groupBy('type')
            ->get();

        // Average completion time
        $avgCompletionTime = Operation::where('status', 'completed')
            ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->whereNotNull('completed_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, completed_at)) as avg_hours')
            ->first();

        // Overdue operations
        $overdueOperations = Operation::with(['location', 'asset'])
            ->where('status', 'pending')
            ->where('scheduled_at', '<', now())
            ->orderBy('scheduled_at')
            ->get();

        return view('reports.operations', compact(
            'operationsByStatus',
            'operationsByType',
            'avgCompletionTime',
            'overdueOperations',
            'startDate',
            'endDate'
        ));
    }
}
