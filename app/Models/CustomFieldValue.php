<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CustomFieldValue extends Model
{
    use HasFactory;

    protected $fillable = [
        'custom_field_id',
        'entity_id',
        'entity_type',
        'value',
    ];

    /**
     * Get the custom field
     */
    public function customField()
    {
        return $this->belongsTo(CustomField::class);
    }

    /**
     * Get the entity this value belongs to
     */
    public function entity()
    {
        return $this->morphTo();
    }

    /**
     * Get formatted value
     */
    public function getFormattedValueAttribute()
    {
        return $this->customField->formatValue($this->value);
    }

    /**
     * Scope for specific entity
     */
    public function scopeForEntity($query, $entityType, $entityId)
    {
        return $query->where('entity_type', $entityType)
                    ->where('entity_id', $entityId);
    }
}
