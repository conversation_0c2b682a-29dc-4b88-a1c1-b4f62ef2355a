<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Operation extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'location_id',
        'asset_id',
        'user_id',
        'operation_code',
        'type',
        'title',
        'description',
        'priority',
        'status',
        'scheduled_at',
        'started_at',
        'completed_at',
        'estimated_duration',
        'actual_duration',
        'cost',
        'notes',
        'checklist',
        'attachments',
    ];

    protected function casts(): array
    {
        return [
            'scheduled_at' => 'datetime',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'estimated_duration' => 'decimal:2',
            'actual_duration' => 'decimal:2',
            'cost' => 'decimal:2',
            'checklist' => 'array',
            'attachments' => 'array',
        ];
    }

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['operation_code', 'type', 'status', 'priority'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the location this operation belongs to
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the asset this operation is related to
     */
    public function asset()
    {
        return $this->belongsTo(Asset::class);
    }

    /**
     * Get the user who performed this operation
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get custom field values for this operation
     */
    public function customFieldValues()
    {
        return $this->morphMany(CustomFieldValue::class, 'entity');
    }

    /**
     * Scope for pending operations
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for in progress operations
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope for completed operations
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for high priority operations
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    /**
     * Scope for operations by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Generate unique operation code
     */
    public static function generateOperationCode($type = null)
    {
        $prefix = match($type) {
            'maintenance' => 'MNT',
            'repair' => 'RPR',
            'collection' => 'COL',
            'restocking' => 'RST',
            'inspection' => 'INS',
            default => 'OPR'
        };

        $date = now()->format('Ymd');
        $lastOperation = static::where('operation_code', 'like', $prefix . $date . '%')
            ->orderBy('operation_code', 'desc')
            ->first();

        if ($lastOperation) {
            $lastNumber = (int) substr($lastOperation->operation_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Start the operation
     */
    public function start()
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);
    }

    /**
     * Complete the operation
     */
    public function complete($notes = null)
    {
        $duration = null;
        if ($this->started_at) {
            $duration = now()->diffInHours($this->started_at, true);
        }

        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'actual_duration' => $duration,
            'notes' => $notes ?: $this->notes,
        ]);
    }

    /**
     * Check if operation is overdue
     */
    public function getIsOverdueAttribute()
    {
        return $this->scheduled_at &&
               $this->scheduled_at->isPast() &&
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get duration difference
     */
    public function getDurationVarianceAttribute()
    {
        if ($this->estimated_duration && $this->actual_duration) {
            return $this->actual_duration - $this->estimated_duration;
        }
        return null;
    }
}
