<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Asset extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'location_id',
        'name',
        'asset_code',
        'qr_code',
        'serial_number',
        'model',
        'manufacturer',
        'description',
        'purchase_price',
        'current_value',
        'purchase_date',
        'warranty_expiry',
        'status',
        'condition',
        'image_path',
        'specifications',
    ];

    protected function casts(): array
    {
        return [
            'purchase_price' => 'decimal:2',
            'current_value' => 'decimal:2',
            'purchase_date' => 'date',
            'warranty_expiry' => 'date',
            'specifications' => 'array',
        ];
    }

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'asset_code', 'status', 'condition', 'location_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the location this asset belongs to
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get financial transactions for this asset
     */
    public function financialTransactions()
    {
        return $this->hasMany(FinancialTransaction::class);
    }

    /**
     * Get operations for this asset
     */
    public function operations()
    {
        return $this->hasMany(Operation::class);
    }

    /**
     * Get custom field values for this asset
     */
    public function customFieldValues()
    {
        return $this->morphMany(CustomFieldValue::class, 'entity');
    }

    /**
     * Scope for active assets
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for assets at specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Get total revenue for this asset
     */
    public function getTotalRevenueAttribute()
    {
        return $this->financialTransactions()
            ->where('type', 'revenue')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get total maintenance cost for this asset
     */
    public function getMaintenanceCostAttribute()
    {
        return $this->financialTransactions()
            ->where('type', 'maintenance')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Check if asset is under warranty
     */
    public function getIsUnderWarrantyAttribute()
    {
        return $this->warranty_expiry && $this->warranty_expiry->isFuture();
    }

    /**
     * Get depreciation value
     */
    public function getDepreciationAttribute()
    {
        if ($this->purchase_price && $this->current_value) {
            return $this->purchase_price - $this->current_value;
        }
        return 0;
    }

    /**
     * Generate unique asset code
     */
    public static function generateAssetCode($locationId = null)
    {
        $prefix = $locationId ? 'L' . str_pad($locationId, 3, '0', STR_PAD_LEFT) : 'AST';
        $lastAsset = static::where('asset_code', 'like', $prefix . '%')
            ->orderBy('asset_code', 'desc')
            ->first();

        if ($lastAsset) {
            $lastNumber = (int) substr($lastAsset->asset_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate unique QR code
     */
    public static function generateQrCode()
    {
        do {
            $qrCode = 'QR' . strtoupper(uniqid());
        } while (static::where('qr_code', $qrCode)->exists());

        return $qrCode;
    }
}
