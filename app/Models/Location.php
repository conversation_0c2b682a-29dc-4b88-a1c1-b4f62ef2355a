<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Location extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'name',
        'address',
        'latitude',
        'longitude',
        'phone_number',
        'contact_person',
        'contact_email',
        'description',
        'status',
        'operating_hours',
    ];

    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'operating_hours' => 'array',
        ];
    }

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'address', 'status', 'contact_person'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get assets at this location
     */
    public function assets()
    {
        return $this->hasMany(Asset::class);
    }

    /**
     * Get financial transactions for this location
     */
    public function financialTransactions()
    {
        return $this->hasMany(FinancialTransaction::class);
    }

    /**
     * Get operations for this location
     */
    public function operations()
    {
        return $this->hasMany(Operation::class);
    }

    /**
     * Get custom field values for this location
     */
    public function customFieldValues()
    {
        return $this->morphMany(CustomFieldValue::class, 'entity');
    }

    /**
     * Scope for active locations
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get total revenue for this location
     */
    public function getTotalRevenueAttribute()
    {
        return $this->financialTransactions()
            ->where('type', 'revenue')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get total expenses for this location
     */
    public function getTotalExpensesAttribute()
    {
        return $this->financialTransactions()
            ->whereIn('type', ['expense', 'maintenance'])
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get profit for this location
     */
    public function getProfitAttribute()
    {
        return $this->total_revenue - $this->total_expenses;
    }

    /**
     * Get coordinates as array
     */
    public function getCoordinatesAttribute()
    {
        if ($this->latitude && $this->longitude) {
            return [
                'lat' => (float) $this->latitude,
                'lng' => (float) $this->longitude,
            ];
        }
        return null;
    }
}
