<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class FinancialTransaction extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'location_id',
        'asset_id',
        'user_id',
        'transaction_code',
        'type',
        'category',
        'amount',
        'currency',
        'description',
        'transaction_date',
        'payment_method',
        'reference_number',
        'metadata',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'transaction_date' => 'date',
            'metadata' => 'array',
        ];
    }

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['transaction_code', 'type', 'amount', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the location this transaction belongs to
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the asset this transaction is related to
     */
    public function asset()
    {
        return $this->belongsTo(Asset::class);
    }

    /**
     * Get the user who recorded this transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for revenue transactions
     */
    public function scopeRevenue($query)
    {
        return $query->where('type', 'revenue');
    }

    /**
     * Scope for expense transactions
     */
    public function scopeExpenses($query)
    {
        return $query->whereIn('type', ['expense', 'maintenance']);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for transactions in date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    /**
     * Generate unique transaction code
     */
    public static function generateTransactionCode($type = null)
    {
        $prefix = match($type) {
            'revenue' => 'REV',
            'expense' => 'EXP',
            'maintenance' => 'MNT',
            'refund' => 'REF',
            'collection' => 'COL',
            default => 'TXN'
        };

        $date = now()->format('Ymd');
        $lastTransaction = static::where('transaction_code', 'like', $prefix . $date . '%')
            ->orderBy('transaction_code', 'desc')
            ->first();

        if ($lastTransaction) {
            $lastNumber = (int) substr($lastTransaction->transaction_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute()
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    /**
     * Check if transaction is revenue
     */
    public function isRevenue()
    {
        return $this->type === 'revenue';
    }

    /**
     * Check if transaction is expense
     */
    public function isExpense()
    {
        return in_array($this->type, ['expense', 'maintenance']);
    }
}
