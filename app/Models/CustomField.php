<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CustomField extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'label',
        'entity_type',
        'field_type',
        'options',
        'default_value',
        'is_required',
        'is_active',
        'sort_order',
        'help_text',
        'validation_rules',
    ];

    protected function casts(): array
    {
        return [
            'options' => 'array',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'validation_rules' => 'array',
        ];
    }

    /**
     * Get custom field values
     */
    public function values()
    {
        return $this->hasMany(CustomFieldValue::class);
    }

    /**
     * Scope for active fields
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for fields by entity type
     */
    public function scopeForEntity($query, $entityType)
    {
        return $query->where('entity_type', $entityType);
    }

    /**
     * Get validation rules array
     */
    public function getValidationRulesArray()
    {
        $rules = [];

        if ($this->is_required) {
            $rules[] = 'required';
        }

        switch ($this->field_type) {
            case 'number':
                $rules[] = 'numeric';
                break;
            case 'decimal':
                $rules[] = 'numeric';
                break;
            case 'date':
                $rules[] = 'date';
                break;
            case 'datetime':
                $rules[] = 'date';
                break;
            case 'boolean':
                $rules[] = 'boolean';
                break;
            case 'select':
                if ($this->options) {
                    $rules[] = 'in:' . implode(',', array_keys($this->options));
                }
                break;
            case 'multiselect':
                $rules[] = 'array';
                if ($this->options) {
                    $rules[] = 'in:' . implode(',', array_keys($this->options));
                }
                break;
            case 'file':
                $rules[] = 'file';
                break;
        }

        // Add custom validation rules
        if ($this->validation_rules) {
            $rules = array_merge($rules, $this->validation_rules);
        }

        return $rules;
    }

    /**
     * Format value based on field type
     */
    public function formatValue($value)
    {
        switch ($this->field_type) {
            case 'boolean':
                return $value ? 'Yes' : 'No';
            case 'select':
                return $this->options[$value] ?? $value;
            case 'multiselect':
                if (is_array($value)) {
                    return collect($value)->map(fn($v) => $this->options[$v] ?? $v)->implode(', ');
                }
                return $value;
            case 'date':
                return $value ? \Carbon\Carbon::parse($value)->format('Y-m-d') : null;
            case 'datetime':
                return $value ? \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s') : null;
            default:
                return $value;
        }
    }
}
