<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\AssetController;
use App\Http\Controllers\FinancialTransactionController;
use App\Http\Controllers\OperationController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\CustomFieldController;
use App\Http\Controllers\ReportsController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User management
    Route::resource('users', UserController::class);
    Route::patch('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Location management
    Route::resource('locations', LocationController::class);

    // Asset management
    Route::resource('assets', AssetController::class);
    Route::get('assets/{asset}/qr-code', [AssetController::class, 'generateQrCode'])->name('assets.qr-code');
    Route::get('assets/{asset}/qr-code/download', [AssetController::class, 'downloadQrCode'])->name('assets.qr-code.download');
    Route::get('assets/qr-scanner', function () { return view('assets.qr-scanner'); })->name('assets.qr-scanner');
    Route::post('assets/qr-scan', [AssetController::class, 'scanQrCode'])->name('assets.qr-scan');
    Route::post('assets/bulk-qr-codes', [AssetController::class, 'bulkQrCodes'])->name('assets.bulk-qr-codes');

    // Financial transactions
    Route::resource('financial-transactions', FinancialTransactionController::class);
    Route::get('reports/financial', [FinancialTransactionController::class, 'reports'])->name('financial.reports');

    // Additional reports (Admin/Manager only)
    Route::middleware(['role:admin,manager'])->group(function () {
        Route::get('reports', [ReportsController::class, 'index'])->name('reports.index');
        Route::get('reports/assets', [ReportsController::class, 'assetPerformance'])->name('reports.assets');
        Route::get('reports/locations', [ReportsController::class, 'locationPerformance'])->name('reports.locations');
        Route::get('reports/operations', [ReportsController::class, 'operations'])->name('reports.operations');
    });

    // Operations
    Route::resource('operations', OperationController::class);
    Route::patch('operations/{operation}/start', [OperationController::class, 'start'])->name('operations.start');
    Route::patch('operations/{operation}/complete', [OperationController::class, 'complete'])->name('operations.complete');

    // Settings (Admin only)
    Route::middleware(['role:admin'])->group(function () {
        Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::post('settings', [SettingsController::class, 'update'])->name('settings.update');
        Route::post('settings/reset', [SettingsController::class, 'reset'])->name('settings.reset');
        Route::resource('custom-fields', CustomFieldController::class);
    });
});

require __DIR__.'/auth.php';
