<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\AssetController;
use App\Http\Controllers\FinancialTransactionController;
use App\Http\Controllers\OperationController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User management
    Route::resource('users', UserController::class);
    Route::patch('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Location management
    Route::resource('locations', LocationController::class);

    // Asset management
    Route::resource('assets', AssetController::class);
    Route::get('assets/{asset}/qr-code', [AssetController::class, 'generateQrCode'])->name('assets.qr-code');

    // Financial transactions
    Route::resource('financial-transactions', FinancialTransactionController::class);
    Route::get('reports/financial', [FinancialTransactionController::class, 'reports'])->name('financial.reports');

    // Operations
    Route::resource('operations', OperationController::class);
    Route::patch('operations/{operation}/start', [OperationController::class, 'start'])->name('operations.start');
    Route::patch('operations/{operation}/complete', [OperationController::class, 'complete'])->name('operations.complete');
});

require __DIR__.'/auth.php';
