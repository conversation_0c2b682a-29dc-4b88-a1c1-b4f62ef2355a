# VendApp User Guide

Welcome to VendApp, your comprehensive vending machine management system. This guide will help you navigate and use all the features of the application.

## Getting Started

### Logging In

1. Navigate to your VendApp URL
2. Enter your email and password
3. Click "Log In"

If you forget your password, click "Forgot Password" to reset it.

### Dashboard Overview

After logging in, you'll see the main dashboard with:

- **Statistics Cards**: Overview of locations, assets, users, and financial data
- **Recent Operations**: Latest maintenance and operational activities
- **Urgent Operations**: High-priority tasks requiring attention
- **Financial Overview**: Revenue, expenses, and performance data (Admin/Manager only)

## User Roles and Permissions

### Admin
- Full system access
- User management
- System settings
- All financial data
- Complete CRUD operations

### Manager
- Location and asset management
- Financial data access
- Operations oversight
- Limited user viewing
- Reporting access

### Operator
- View locations and assets
- Record operations
- Create financial transactions
- Limited system access

## Core Features

### Location Management

#### Viewing Locations
1. Click "Locations" in the navigation menu
2. Use search and filters to find specific locations
3. View location cards with key information

#### Adding a New Location
1. Click "Add New Location" button
2. Fill in required information:
   - **Name**: Location identifier
   - **Address**: Full physical address
   - **Coordinates**: Latitude and longitude (optional)
   - **Contact Information**: Person and phone number
   - **Operating Hours**: Business hours for each day
   - **Status**: Active, Inactive, or Maintenance

3. Add any custom fields if configured
4. Click "Save" to create the location

#### Editing Locations
1. Find the location in the list
2. Click "Edit" button
3. Modify the information as needed
4. Save changes

### Asset Management

#### Viewing Assets
1. Navigate to "Assets" in the menu
2. Browse assets in grid or list view
3. Filter by location, status, or condition
4. Search by name, asset code, or serial number

#### Adding New Assets
1. Click "Add New Asset"
2. Select the location where the asset will be placed
3. Enter asset details:
   - **Name**: Descriptive name for the asset
   - **Model and Manufacturer**: Equipment specifications
   - **Serial Number**: Unique identifier
   - **Purchase Information**: Price and date
   - **Warranty**: Expiry date
   - **Condition**: Current state (Excellent, Good, Fair, Poor)

4. Upload an image if available
5. Add specifications and custom field data
6. Save the asset

#### QR Code Generation
1. View any asset
2. Click "QR" button to generate QR code
3. Print or save the QR code for physical labeling
4. Use QR codes for quick asset identification

### Operations Management

#### Recording Operations
1. Navigate to "Operations"
2. Click "Add New Operation"
3. Select location and asset (if applicable)
4. Choose operation type:
   - **Maintenance**: Routine upkeep
   - **Repair**: Fix issues
   - **Collection**: Money collection
   - **Restocking**: Inventory replenishment
   - **Inspection**: Regular checks

5. Set priority level and schedule
6. Add detailed description and notes
7. Save the operation

#### Managing Operation Status
- **Pending**: Operation is scheduled but not started
- **In Progress**: Currently being worked on
- **Completed**: Finished successfully
- **Cancelled**: No longer needed

#### Operation Workflow
1. Create operation with "Pending" status
2. Start operation when beginning work
3. Add notes and updates during execution
4. Mark as "Completed" when finished
5. Record actual time spent and costs

### Financial Tracking

#### Recording Transactions
1. Go to "Financials" (if you have access)
2. Click "Add Transaction"
3. Select transaction type:
   - **Revenue**: Money collected from machines
   - **Expense**: Operating costs
   - **Maintenance**: Repair and upkeep costs
   - **Refund**: Customer refunds

4. Enter amount and description
5. Associate with location and asset
6. Set transaction date
7. Save the record

#### Viewing Financial Reports
- **Dashboard**: Quick overview of key metrics
- **Monthly Performance**: Current month revenue and expenses
- **Location Performance**: Compare different locations
- **Asset Profitability**: Revenue per asset

### User Management (Admin/Manager Only)

#### Adding Users
1. Navigate to "Users"
2. Click "Add New User"
3. Enter user information:
   - **Basic Info**: Name, email, employee ID
   - **Contact**: Phone number
   - **Employment**: Department, position, hire date
   - **Account**: Password and status

4. Assign roles (Admin, Manager, Operator)
5. Add custom field data if configured
6. Save the user account

#### Managing User Status
- **Active**: User can log in and use the system
- **Inactive**: User cannot log in
- **Suspended**: Temporarily disabled access

## Advanced Features

### Custom Fields

If your administrator has configured custom fields, you'll see additional form fields when creating or editing:

- **Text Fields**: Single-line text input
- **Text Areas**: Multi-line text input
- **Numbers**: Numeric values
- **Dates**: Date picker
- **Dropdowns**: Select from predefined options
- **Checkboxes**: Yes/no options
- **File Uploads**: Attach documents or images

### Search and Filtering

Most list views include powerful search and filtering:

1. **Search Bar**: Type keywords to find items
2. **Status Filters**: Filter by active, inactive, etc.
3. **Category Filters**: Filter by type, location, etc.
4. **Date Ranges**: Filter by time periods
5. **Clear Filters**: Reset all filters

### Notifications and Alerts

The system may show notifications for:

- **Urgent Operations**: High-priority tasks
- **Overdue Maintenance**: Scheduled maintenance past due
- **Low Cash Alerts**: When collection is needed
- **System Messages**: Important updates

## Best Practices

### Data Entry
- Use consistent naming conventions
- Fill in all required fields
- Add detailed descriptions for operations
- Upload images when possible
- Keep contact information current

### Security
- Use strong passwords
- Log out when finished
- Don't share login credentials
- Report suspicious activity

### Maintenance Workflow
1. **Schedule Regular Inspections**: Create recurring operations
2. **Document Everything**: Add detailed notes to operations
3. **Track Costs**: Record all expenses for accurate reporting
4. **Monitor Performance**: Review financial reports regularly
5. **Update Asset Conditions**: Keep condition status current

### Financial Management
- Record transactions promptly
- Categorize expenses properly
- Review reports monthly
- Track asset profitability
- Monitor location performance

## Troubleshooting

### Common Issues

**Can't Log In**
- Check email and password
- Use "Forgot Password" if needed
- Contact administrator if account is locked

**Missing Data**
- Check filters and search terms
- Verify you have permission to view the data
- Contact administrator if data should be visible

**Form Errors**
- Check all required fields are filled
- Verify data formats (dates, numbers)
- Check file size limits for uploads

**Performance Issues**
- Clear browser cache
- Check internet connection
- Contact administrator if problems persist

### Getting Help

1. **Check this user guide** for feature explanations
2. **Contact your administrator** for account issues
3. **Report bugs** through your organization's process
4. **Request training** if you need additional help

## Mobile Usage

VendApp is responsive and works on mobile devices:

- **Touch-friendly interface**: Easy navigation on tablets and phones
- **Responsive design**: Adapts to different screen sizes
- **Quick actions**: Essential functions accessible on mobile
- **Offline capability**: Some features may work offline (if configured)

## Tips for Efficiency

1. **Use bookmarks** for frequently accessed pages
2. **Learn keyboard shortcuts** for faster navigation
3. **Set up filters** for your common searches
4. **Use bulk operations** when available
5. **Keep browser updated** for best performance

## Data Export and Reporting

- **Financial Reports**: Export transaction data to Excel
- **Asset Lists**: Download asset inventories
- **Operation Logs**: Export maintenance records
- **Custom Reports**: Request specific reports from administrator

---

**Need more help?** Contact your system administrator or refer to the technical documentation for advanced features.

**VendApp** - Streamlining your vending machine operations! 🎯
