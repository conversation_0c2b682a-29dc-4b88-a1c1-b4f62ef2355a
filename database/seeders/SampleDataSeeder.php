<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Location;
use App\Models\Asset;
use App\Models\AppSetting;
use Illuminate\Support\Facades\Hash;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP001',
            'department' => 'Administration',
            'position' => 'System Administrator',
            'status' => 'active',
            'hire_date' => now()->subYears(2),
        ]);
        $admin->assignRole('admin');

        // Create manager user
        $manager = User::create([
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP002',
            'department' => 'Operations',
            'position' => 'Operations Manager',
            'status' => 'active',
            'hire_date' => now()->subYear(),
        ]);
        $manager->assignRole('manager');

        // Create operator user
        $operator = User::create([
            'name' => 'Operator User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP003',
            'department' => 'Operations',
            'position' => 'Field Operator',
            'status' => 'active',
            'hire_date' => now()->subMonths(6),
        ]);
        $operator->assignRole('operator');

        // Create sample locations
        $locations = [
            [
                'name' => 'Downtown Office Building',
                'address' => '123 Main Street, Downtown, City, State 12345',
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'phone_number' => '******-0101',
                'contact_person' => 'John Smith',
                'contact_email' => '<EMAIL>',
                'status' => 'active',
                'operating_hours' => [
                    'monday' => ['open' => '08:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '18:00'],
                    'thursday' => ['open' => '08:00', 'close' => '18:00'],
                    'friday' => ['open' => '08:00', 'close' => '18:00'],
                    'saturday' => ['open' => '09:00', 'close' => '17:00'],
                    'sunday' => ['closed' => true],
                ],
            ],
            [
                'name' => 'University Campus',
                'address' => '456 University Ave, Campus, City, State 12346',
                'latitude' => 40.7589,
                'longitude' => -73.9851,
                'phone_number' => '******-0102',
                'contact_person' => 'Jane Doe',
                'contact_email' => '<EMAIL>',
                'status' => 'active',
                'operating_hours' => [
                    'monday' => ['open' => '06:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '06:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '06:00', 'close' => '22:00'],
                    'thursday' => ['open' => '06:00', 'close' => '22:00'],
                    'friday' => ['open' => '06:00', 'close' => '22:00'],
                    'saturday' => ['open' => '08:00', 'close' => '20:00'],
                    'sunday' => ['open' => '10:00', 'close' => '18:00'],
                ],
            ],
            [
                'name' => 'Shopping Mall Food Court',
                'address' => '789 Mall Drive, Shopping District, City, State 12347',
                'latitude' => 40.7505,
                'longitude' => -73.9934,
                'phone_number' => '******-0103',
                'contact_person' => 'Mike Johnson',
                'contact_email' => '<EMAIL>',
                'status' => 'active',
                'operating_hours' => [
                    'monday' => ['open' => '10:00', 'close' => '21:00'],
                    'tuesday' => ['open' => '10:00', 'close' => '21:00'],
                    'wednesday' => ['open' => '10:00', 'close' => '21:00'],
                    'thursday' => ['open' => '10:00', 'close' => '21:00'],
                    'friday' => ['open' => '10:00', 'close' => '22:00'],
                    'saturday' => ['open' => '10:00', 'close' => '22:00'],
                    'sunday' => ['open' => '11:00', 'close' => '20:00'],
                ],
            ],
        ];

        foreach ($locations as $locationData) {
            Location::create($locationData);
        }

        // Create sample assets
        $locations = Location::all();
        foreach ($locations as $location) {
            for ($i = 1; $i <= 3; $i++) {
                Asset::create([
                    'location_id' => $location->id,
                    'name' => "Vending Machine {$i}",
                    'asset_code' => Asset::generateAssetCode($location->id),
                    'qr_code' => Asset::generateQrCode(),
                    'serial_number' => 'VM' . str_pad($location->id, 2, '0', STR_PAD_LEFT) . str_pad($i, 3, '0', STR_PAD_LEFT),
                    'model' => 'VendMax Pro 3000',
                    'manufacturer' => 'VendTech Industries',
                    'description' => 'High-capacity snack and beverage vending machine',
                    'purchase_price' => 5000.00,
                    'current_value' => 4000.00,
                    'purchase_date' => now()->subMonths(rand(6, 24)),
                    'warranty_expiry' => now()->addMonths(rand(6, 18)),
                    'status' => 'active',
                    'condition' => ['excellent', 'good', 'fair'][rand(0, 2)],
                    'specifications' => [
                        'capacity' => '400 items',
                        'payment_methods' => ['cash', 'card', 'mobile'],
                        'power_consumption' => '150W',
                        'dimensions' => '72" H x 39" W x 35" D',
                        'weight' => '750 lbs',
                    ],
                ]);
            }
        }

        // Create app settings
        $settings = [
            ['key' => 'app_name', 'value' => 'VendApp - Vending Machine Management', 'type' => 'string', 'group' => 'general', 'label' => 'Application Name', 'is_public' => true],
            ['key' => 'currency', 'value' => 'USD', 'type' => 'string', 'group' => 'financial', 'label' => 'Default Currency', 'is_public' => true],
            ['key' => 'date_format', 'value' => 'Y-m-d', 'type' => 'string', 'group' => 'general', 'label' => 'Date Format', 'is_public' => true],
            ['key' => 'timezone', 'value' => 'America/New_York', 'type' => 'string', 'group' => 'general', 'label' => 'Timezone', 'is_public' => true],
            ['key' => 'maintenance_reminder_days', 'value' => '30', 'type' => 'integer', 'group' => 'operations', 'label' => 'Maintenance Reminder (Days)', 'is_public' => false],
            ['key' => 'low_cash_threshold', 'value' => '100', 'type' => 'integer', 'group' => 'financial', 'label' => 'Low Cash Alert Threshold', 'is_public' => false],
        ];

        foreach ($settings as $setting) {
            AppSetting::create($setting);
        }
    }
}
