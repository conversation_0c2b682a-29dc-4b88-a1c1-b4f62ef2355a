<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $managerRole = Role::create(['name' => 'manager']);
        $operatorRole = Role::create(['name' => 'operator']);

        // Create permissions
        $permissions = [
            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Location permissions
            'view locations',
            'create locations',
            'edit locations',
            'delete locations',

            // Asset permissions
            'view assets',
            'create assets',
            'edit assets',
            'delete assets',

            // Financial permissions
            'view financials',
            'create financials',
            'edit financials',
            'delete financials',
            'view reports',

            // Operation permissions
            'view operations',
            'create operations',
            'edit operations',
            'delete operations',
            'perform operations',

            // Settings permissions
            'manage settings',
            'manage custom fields',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to roles

        // Admin gets all permissions
        $adminRole->givePermissionTo(Permission::all());

        // Manager gets most permissions except user/settings management
        $managerRole->givePermissionTo([
            'view users',
            'view locations', 'create locations', 'edit locations',
            'view assets', 'create assets', 'edit assets',
            'view financials', 'create financials', 'edit financials', 'view reports',
            'view operations', 'create operations', 'edit operations', 'perform operations',
        ]);

        // Operator gets basic permissions
        $operatorRole->givePermissionTo([
            'view locations',
            'view assets',
            'create financials', 'view financials',
            'view operations', 'create operations', 'perform operations',
        ]);
    }
}
