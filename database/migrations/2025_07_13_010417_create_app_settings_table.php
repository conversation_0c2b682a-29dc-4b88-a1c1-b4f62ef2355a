<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // Setting key
            $table->text('value')->nullable(); // Setting value
            $table->string('type')->default('string'); // Data type: string, integer, boolean, json
            $table->string('group')->default('general'); // Setting group for organization
            $table->string('label'); // Human-readable label
            $table->text('description')->nullable(); // Setting description
            $table->boolean('is_public')->default(false); // Whether setting can be accessed publicly
            $table->timestamps();

            $table->index(['key']);
            $table->index(['group']);
            $table->index(['is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_settings');
    }
};
