<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('location_id')->constrained()->onDelete('cascade');
            $table->foreignId('asset_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Who performed the operation
            $table->string('operation_code')->unique(); // Unique operation identifier
            $table->string('type'); // e.g., 'maintenance', 'repair', 'collection', 'restocking', 'inspection'
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->datetime('scheduled_at')->nullable();
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->decimal('estimated_duration', 5, 2)->nullable(); // Hours
            $table->decimal('actual_duration', 5, 2)->nullable(); // Hours
            $table->decimal('cost', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->json('checklist')->nullable(); // Store operation checklist as JSON
            $table->json('attachments')->nullable(); // Store file paths as JSON
            $table->timestamps();

            $table->index(['location_id', 'status']);
            $table->index(['asset_id', 'status']);
            $table->index(['type', 'status']);
            $table->index(['status', 'scheduled_at']);
            $table->index(['user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operations');
    }
};
