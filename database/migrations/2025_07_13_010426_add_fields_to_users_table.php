<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('employee_id')->nullable()->unique()->after('email');
            $table->string('phone_number')->nullable()->after('employee_id');
            $table->string('department')->nullable()->after('phone_number');
            $table->string('position')->nullable()->after('department');
            $table->date('hire_date')->nullable()->after('position');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('hire_date');
            $table->string('avatar')->nullable()->after('status');
            $table->text('bio')->nullable()->after('avatar');
            $table->json('preferences')->nullable()->after('bio'); // User preferences as JSON
            $table->timestamp('last_login_at')->nullable()->after('preferences');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'employee_id',
                'phone_number',
                'department',
                'position',
                'hire_date',
                'status',
                'avatar',
                'bio',
                'preferences',
                'last_login_at'
            ]);
        });
    }
};
