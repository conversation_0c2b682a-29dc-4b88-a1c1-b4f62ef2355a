<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_fields', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Field name
            $table->string('label'); // Display label
            $table->enum('entity_type', ['user', 'location', 'asset', 'operation']); // Which entity this field applies to
            $table->enum('field_type', ['text', 'textarea', 'number', 'decimal', 'date', 'datetime', 'boolean', 'select', 'multiselect', 'file']);
            $table->json('options')->nullable(); // For select/multiselect fields
            $table->text('default_value')->nullable();
            $table->boolean('is_required')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->text('help_text')->nullable();
            $table->json('validation_rules')->nullable(); // Store validation rules as JSON
            $table->timestamps();

            $table->unique(['name', 'entity_type']);
            $table->index(['entity_type', 'is_active']);
            $table->index(['sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_fields');
    }
};
