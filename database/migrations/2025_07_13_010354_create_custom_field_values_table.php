<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_field_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('custom_field_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('entity_id'); // ID of the entity (user, location, asset, operation)
            $table->string('entity_type'); // Type of entity
            $table->text('value')->nullable(); // The actual field value
            $table->timestamps();

            $table->unique(['custom_field_id', 'entity_id', 'entity_type']);
            $table->index(['entity_id', 'entity_type']);
            $table->index(['custom_field_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_field_values');
    }
};
