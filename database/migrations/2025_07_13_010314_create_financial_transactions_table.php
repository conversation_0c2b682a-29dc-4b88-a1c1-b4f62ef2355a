<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('location_id')->constrained()->onDelete('cascade');
            $table->foreignId('asset_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Who recorded the transaction
            $table->string('transaction_code')->unique(); // Unique transaction identifier
            $table->enum('type', ['revenue', 'expense', 'maintenance', 'refund', 'collection']);
            $table->string('category')->nullable(); // e.g., 'cash_collection', 'repair_cost', 'restocking'
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->text('description')->nullable();
            $table->date('transaction_date');
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'other'])->nullable();
            $table->string('reference_number')->nullable(); // Receipt number, invoice number, etc.
            $table->json('metadata')->nullable(); // Additional transaction data
            $table->enum('status', ['pending', 'completed', 'cancelled'])->default('completed');
            $table->timestamps();

            $table->index(['location_id', 'transaction_date']);
            $table->index(['asset_id', 'transaction_date']);
            $table->index(['type', 'transaction_date']);
            $table->index(['transaction_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_transactions');
    }
};
