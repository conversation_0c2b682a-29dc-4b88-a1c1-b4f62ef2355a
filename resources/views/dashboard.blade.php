<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('VendApp Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Locations Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Locations</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $stats['total_locations'] }}</div>
                                <div class="text-sm text-green-600">{{ $stats['active_locations'] }} active</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assets Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Assets</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $stats['total_assets'] }}</div>
                                <div class="text-sm text-green-600">{{ $stats['active_assets'] }} active</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Users</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $stats['total_users'] }}</div>
                                <div class="text-sm text-green-600">{{ $stats['active_users'] }} active</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Card (Admin/Manager only) -->
                @if(auth()->user()->hasAnyRole(['admin', 'manager']))
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Monthly Profit</div>
                                <div class="text-2xl font-bold text-gray-900">${{ number_format($financialData['monthly_profit'] ?? 0, 2) }}</div>
                                <div class="text-sm text-gray-600">Revenue: ${{ number_format($financialData['monthly_revenue'] ?? 0, 2) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Recent Operations -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Operations</h3>
                        @if($recentOperations->count() > 0)
                            <div class="space-y-3">
                                @foreach($recentOperations as $operation)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $operation->title }}</div>
                                        <div class="text-sm text-gray-600">{{ $operation->location->name }} - {{ $operation->asset->name ?? 'General' }}</div>
                                        <div class="text-xs text-gray-500">{{ $operation->created_at->diffForHumans() }}</div>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        @if($operation->status === 'completed') bg-green-100 text-green-800
                                        @elseif($operation->status === 'in_progress') bg-blue-100 text-blue-800
                                        @elseif($operation->status === 'pending') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($operation->status) }}
                                    </span>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500">No recent operations</p>
                        @endif
                    </div>
                </div>

                <!-- Urgent Operations -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Urgent Operations</h3>
                        @if($urgentOperations->count() > 0)
                            <div class="space-y-3">
                                @foreach($urgentOperations as $operation)
                                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $operation->title }}</div>
                                        <div class="text-sm text-gray-600">{{ $operation->location->name }} - {{ $operation->asset->name ?? 'General' }}</div>
                                        @if($operation->scheduled_at)
                                        <div class="text-xs text-red-600">Due: {{ $operation->scheduled_at->format('M j, Y') }}</div>
                                        @endif
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        @if($operation->priority === 'urgent') bg-red-100 text-red-800
                                        @else bg-orange-100 text-orange-800 @endif">
                                        {{ ucfirst($operation->priority) }}
                                    </span>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500">No urgent operations</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Financial Overview (Admin/Manager only) -->
            @if(auth()->user()->hasAnyRole(['admin', 'manager']))
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Transactions -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Transactions</h3>
                        @if($recentTransactions->count() > 0)
                            <div class="space-y-3">
                                @foreach($recentTransactions as $transaction)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $transaction->description ?: $transaction->category }}</div>
                                        <div class="text-sm text-gray-600">{{ $transaction->location->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $transaction->transaction_date->format('M j, Y') }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium
                                            @if($transaction->type === 'revenue') text-green-600
                                            @else text-red-600 @endif">
                                            @if($transaction->type === 'revenue') + @else - @endif${{ number_format($transaction->amount, 2) }}
                                        </div>
                                        <div class="text-xs text-gray-500">{{ ucfirst($transaction->type) }}</div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500">No recent transactions</p>
                        @endif
                    </div>
                </div>

                <!-- Top Performing Locations -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Top Performing Locations (This Month)</h3>
                        @if($locationPerformance->count() > 0)
                            <div class="space-y-3">
                                @foreach($locationPerformance as $location)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $location['name'] }}</div>
                                        <div class="text-sm text-gray-600">Revenue: ${{ number_format($location['revenue'], 2) }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-green-600">${{ number_format($location['profit'], 2) }}</div>
                                        <div class="text-xs text-gray-500">Profit</div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500">No performance data available</p>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</x-app-layout>
