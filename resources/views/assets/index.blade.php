<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Assets') }}
            </h2>
            @can('create', App\Models\Asset::class)
            <a href="{{ route('assets.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add New Asset
            </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('assets.index') }}" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search assets..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="location_id" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Locations</option>
                                @foreach($locations as $location)
                                <option value="{{ $location->id }}" {{ request('location_id') == $location->id ? 'selected' : '' }}>
                                    {{ $location->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <select name="status" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="maintenance" {{ request('status') === 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                <option value="retired" {{ request('status') === 'retired' ? 'selected' : '' }}>Retired</option>
                            </select>
                        </div>
                        <div>
                            <select name="condition" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Conditions</option>
                                <option value="excellent" {{ request('condition') === 'excellent' ? 'selected' : '' }}>Excellent</option>
                                <option value="good" {{ request('condition') === 'good' ? 'selected' : '' }}>Good</option>
                                <option value="fair" {{ request('condition') === 'fair' ? 'selected' : '' }}>Fair</option>
                                <option value="poor" {{ request('condition') === 'poor' ? 'selected' : '' }}>Poor</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Search
                        </button>
                        @if(request()->hasAny(['search', 'location_id', 'status', 'condition']))
                        <a href="{{ route('assets.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Clear
                        </a>
                        @endif
                    </form>
                </div>
            </div>

            <!-- Assets Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($assets as $asset)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">{{ $asset->name }}</h3>
                                <p class="text-sm text-gray-600">{{ $asset->asset_code }}</p>
                            </div>
                            <div class="flex flex-col items-end space-y-1">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    @if($asset->status === 'active') bg-green-100 text-green-800
                                    @elseif($asset->status === 'inactive') bg-red-100 text-red-800
                                    @elseif($asset->status === 'maintenance') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($asset->status) }}
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    @if($asset->condition === 'excellent') bg-green-100 text-green-800
                                    @elseif($asset->condition === 'good') bg-blue-100 text-blue-800
                                    @elseif($asset->condition === 'fair') bg-yellow-100 text-yellow-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ ucfirst($asset->condition) }}
                                </span>
                            </div>
                        </div>
                        
                        @if($asset->image_path)
                        <div class="mb-4">
                            <img src="{{ Storage::url($asset->image_path) }}" alt="{{ $asset->name }}" 
                                 class="w-full h-32 object-cover rounded-lg">
                        </div>
                        @endif
                        
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>{{ $asset->location->name }}</span>
                            </div>
                            
                            @if($asset->model)
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{{ $asset->model }}</span>
                            </div>
                            @endif
                            
                            @if($asset->serial_number)
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                <span>SN: {{ $asset->serial_number }}</span>
                            </div>
                            @endif
                            
                            @if($asset->purchase_price)
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <span>Value: ${{ number_format($asset->current_value ?: $asset->purchase_price, 2) }}</span>
                            </div>
                            @endif
                        </div>
                        
                        @if($asset->operations->count() > 0)
                        <div class="text-xs text-gray-500 mb-4">
                            Last operation: {{ $asset->operations->first()->created_at->diffForHumans() }}
                        </div>
                        @endif
                        
                        <div class="flex space-x-2">
                            <a href="{{ route('assets.show', $asset) }}" 
                               class="flex-1 bg-blue-500 hover:bg-blue-700 text-white text-center py-2 px-3 rounded text-sm">
                                View
                            </a>
                            <a href="{{ route('assets.qr-code', $asset) }}" 
                               class="bg-gray-500 hover:bg-gray-700 text-white py-2 px-3 rounded text-sm">
                                QR
                            </a>
                            @can('update', $asset)
                            <a href="{{ route('assets.edit', $asset) }}" 
                               class="bg-green-500 hover:bg-green-700 text-white py-2 px-3 rounded text-sm">
                                Edit
                            </a>
                            @endcan
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-span-full">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No assets found</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by adding a new asset.</p>
                            @can('create', App\Models\Asset::class)
                            <div class="mt-6">
                                <a href="{{ route('assets.create') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    Add New Asset
                                </a>
                            </div>
                            @endcan
                        </div>
                    </div>
                </div>
                @endforelse
            </div>
            
            <!-- Pagination -->
            @if($assets->hasPages())
            <div class="mt-6">
                {{ $assets->links() }}
            </div>
            @endif
        </div>
    </div>
</x-app-layout>
