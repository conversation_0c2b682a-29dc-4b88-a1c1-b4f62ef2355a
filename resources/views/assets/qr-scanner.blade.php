<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('QR Code Scanner') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="text-center mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Scan Asset QR Code</h3>
                        <p class="text-sm text-gray-600">Use your device's camera to scan an asset QR code, or manually enter the QR code data below.</p>
                    </div>

                    <!-- Camera Scanner (if supported) -->
                    <div id="camera-section" class="mb-6">
                        <div class="text-center">
                            <button id="start-camera" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4">
                                Start Camera Scanner
                            </button>
                        </div>
                        <div id="camera-container" class="hidden">
                            <video id="camera-feed" class="w-full max-w-md mx-auto border border-gray-300 rounded"></video>
                            <div class="text-center mt-2">
                                <button id="stop-camera" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    Stop Camera
                                </button>
                            </div>
                        </div>
                        <div id="camera-error" class="hidden text-red-600 text-center mt-2"></div>
                    </div>

                    <div class="text-center text-gray-500 mb-6">
                        <span>OR</span>
                    </div>

                    <!-- Manual Input -->
                    <div class="max-w-md mx-auto">
                        <form method="POST" action="{{ route('assets.qr-scan') }}">
                            @csrf
                            <div class="mb-4">
                                <label for="qr_data" class="block text-sm font-medium text-gray-700 mb-2">
                                    QR Code Data
                                </label>
                                <textarea id="qr_data" name="qr_data" rows="4" 
                                          placeholder="Paste or type the QR code data here..."
                                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                          required>{{ old('qr_data') }}</textarea>
                                @error('qr_data')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="flex justify-center space-x-3">
                                <a href="{{ route('assets.index') }}" 
                                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                    Cancel
                                </a>
                                <button type="submit" 
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Process QR Code
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Instructions -->
                    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">How to use QR Scanner</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li><strong>Camera Scanner:</strong> Click "Start Camera Scanner" to use your device's camera to scan QR codes directly.</li>
                                        <li><strong>Manual Input:</strong> If you have the QR code data as text, paste it into the text area below.</li>
                                        <li><strong>Asset QR Codes:</strong> Only QR codes generated by this system for assets will work.</li>
                                        <li><strong>Quick Access:</strong> Successfully scanned QR codes will take you directly to the asset details page.</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let stream = null;
        let scanning = false;

        document.getElementById('start-camera').addEventListener('click', async function() {
            try {
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera access is not supported in this browser');
                }

                // Request camera access
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        facingMode: 'environment' // Use back camera if available
                    } 
                });

                const video = document.getElementById('camera-feed');
                video.srcObject = stream;
                video.play();

                // Show camera container and hide start button
                document.getElementById('camera-container').classList.remove('hidden');
                document.getElementById('start-camera').classList.add('hidden');
                document.getElementById('camera-error').classList.add('hidden');

                // Start scanning (this is a simplified version - in production you'd use a QR code library)
                startScanning();

            } catch (error) {
                console.error('Error accessing camera:', error);
                document.getElementById('camera-error').textContent = 'Error: ' + error.message;
                document.getElementById('camera-error').classList.remove('hidden');
            }
        });

        document.getElementById('stop-camera').addEventListener('click', function() {
            stopCamera();
        });

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            scanning = false;
            document.getElementById('camera-container').classList.add('hidden');
            document.getElementById('start-camera').classList.remove('hidden');
        }

        function startScanning() {
            scanning = true;
            // Note: This is a placeholder for QR code scanning functionality
            // In a production environment, you would integrate a QR code scanning library
            // such as jsQR, QuaggaJS, or ZXing-js
            
            const video = document.getElementById('camera-feed');
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            function scan() {
                if (!scanning) return;

                if (video.readyState === video.HAVE_ENOUGH_DATA) {
                    canvas.height = video.videoHeight;
                    canvas.width = video.videoWidth;
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    
                    // Here you would use a QR code library to decode the image
                    // For now, we'll just show a message
                    console.log('Scanning for QR codes...');
                }

                requestAnimationFrame(scan);
            }
            scan();
        }

        // Clean up when leaving the page
        window.addEventListener('beforeunload', function() {
            stopCamera();
        });

        // Show a note about QR scanning libraries
        console.log('Note: This is a basic camera implementation. For production use, integrate a QR code scanning library like jsQR or QuaggaJS.');
    </script>
</x-app-layout>
