<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Asset Details') }}: {{ $asset->name }}
            </h2>
            <div class="flex space-x-2">
                @can('update', $asset)
                <a href="{{ route('assets.edit', $asset) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit Asset
                </a>
                @endcan
                <a href="{{ route('assets.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Assets
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Asset Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Asset Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Asset Code</label>
                                    <div class="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">{{ $asset->asset_code }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Name</label>
                                    <div class="mt-1 text-sm text-gray-900">{{ $asset->name }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Location</label>
                                    <div class="mt-1 text-sm text-gray-900">
                                        <a href="{{ route('locations.show', $asset->location) }}" class="text-blue-600 hover:text-blue-900">
                                            {{ $asset->location->name }}
                                        </a>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <div class="mt-1">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($asset->status === 'active') bg-green-100 text-green-800
                                            @elseif($asset->status === 'maintenance') bg-yellow-100 text-yellow-800
                                            @elseif($asset->status === 'inactive') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ ucfirst($asset->status) }}
                                        </span>
                                    </div>
                                </div>
                                
                                @if($asset->model)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Model</label>
                                    <div class="mt-1 text-sm text-gray-900">{{ $asset->model }}</div>
                                </div>
                                @endif
                                
                                @if($asset->serial_number)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Serial Number</label>
                                    <div class="mt-1 text-sm text-gray-900 font-mono">{{ $asset->serial_number }}</div>
                                </div>
                                @endif
                                
                                @if($asset->purchase_date)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Purchase Date</label>
                                    <div class="mt-1 text-sm text-gray-900">{{ $asset->purchase_date->format('M j, Y') }}</div>
                                </div>
                                @endif
                                
                                @if($asset->purchase_price)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Purchase Price</label>
                                    <div class="mt-1 text-sm text-gray-900">${{ number_format($asset->purchase_price, 2) }}</div>
                                </div>
                                @endif
                                
                                @if($asset->warranty_expiry)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Warranty Expiry</label>
                                    <div class="mt-1 text-sm text-gray-900">{{ $asset->warranty_expiry->format('M j, Y') }}</div>
                                </div>
                                @endif
                            </div>
                            
                            @if($asset->description)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">Description</label>
                                <div class="mt-1 text-sm text-gray-900">{{ $asset->description }}</div>
                            </div>
                            @endif
                            
                            @if($asset->notes)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">Notes</label>
                                <div class="mt-1 text-sm text-gray-900">{{ $asset->notes }}</div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Custom Fields -->
                    @if($asset->customFieldValues->count() > 0)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @foreach($asset->customFieldValues as $fieldValue)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">{{ $fieldValue->customField->label }}</label>
                                    <div class="mt-1 text-sm text-gray-900">{{ $fieldValue->display_value }}</div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- QR Code and Actions -->
                <div class="space-y-6">
                    <!-- QR Code -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">QR Code</h3>
                            <div class="text-center">
                                <div class="mb-4">
                                    <img src="{{ route('assets.qr-code', $asset) }}" 
                                         alt="QR Code for {{ $asset->name }}" 
                                         class="mx-auto border border-gray-200 rounded">
                                </div>
                                <div class="space-y-2">
                                    <a href="{{ route('assets.qr-code', $asset) }}" 
                                       target="_blank"
                                       class="block w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center">
                                        View QR Code
                                    </a>
                                    <a href="{{ route('assets.qr-code.download', $asset) }}" 
                                       class="block w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center">
                                        Download PNG
                                    </a>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">
                                    Scan this QR code to quickly access asset information
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-2">
                                @can('create', App\Models\Operation::class)
                                <a href="{{ route('operations.create', ['asset_id' => $asset->id]) }}" 
                                   class="block w-full bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded text-center">
                                    Schedule Operation
                                </a>
                                @endcan
                                
                                @can('create', App\Models\FinancialTransaction::class)
                                <a href="{{ route('financial-transactions.create', ['asset_id' => $asset->id]) }}" 
                                   class="block w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center">
                                    Add Transaction
                                </a>
                                @endcan
                                
                                <a href="{{ route('assets.index', ['location_id' => $asset->location_id]) }}" 
                                   class="block w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-center">
                                    View Location Assets
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Asset Statistics -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Total Revenue:</span>
                                    <span class="text-sm font-medium text-green-600">
                                        ${{ number_format($asset->financialTransactions()->where('type', 'revenue')->where('status', 'completed')->sum('amount'), 2) }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Maintenance Cost:</span>
                                    <span class="text-sm font-medium text-red-600">
                                        ${{ number_format($asset->financialTransactions()->where('type', 'maintenance')->where('status', 'completed')->sum('amount'), 2) }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Operations:</span>
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ $asset->operations()->count() }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Last Operation:</span>
                                    <span class="text-sm font-medium text-gray-900">
                                        @if($asset->operations()->latest()->first())
                                            {{ $asset->operations()->latest()->first()->created_at->diffForHumans() }}
                                        @else
                                            Never
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="mt-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                        
                        <!-- Recent Operations -->
                        @if($asset->operations()->count() > 0)
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-700 mb-2">Recent Operations</h4>
                            <div class="space-y-2">
                                @foreach($asset->operations()->with('user')->latest()->take(5)->get() as $operation)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $operation->title }}</div>
                                        <div class="text-sm text-gray-600">{{ $operation->type }} - {{ $operation->user->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $operation->created_at->format('M j, Y g:i A') }}</div>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        @if($operation->status === 'completed') bg-green-100 text-green-800
                                        @elseif($operation->status === 'in_progress') bg-blue-100 text-blue-800
                                        @elseif($operation->status === 'pending') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst(str_replace('_', ' ', $operation->status)) }}
                                    </span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Recent Transactions -->
                        @if($asset->financialTransactions()->count() > 0)
                        <div>
                            <h4 class="text-md font-medium text-gray-700 mb-2">Recent Transactions</h4>
                            <div class="space-y-2">
                                @foreach($asset->financialTransactions()->with('user')->latest('transaction_date')->take(5)->get() as $transaction)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $transaction->transaction_code }}</div>
                                        <div class="text-sm text-gray-600">{{ $transaction->description ?: $transaction->category }}</div>
                                        <div class="text-xs text-gray-500">{{ $transaction->transaction_date->format('M j, Y') }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium {{ $transaction->type === 'revenue' ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $transaction->type === 'revenue' ? '+' : '-' }}${{ number_format($transaction->amount, 2) }}
                                        </div>
                                        <div class="text-xs text-gray-500">{{ ucfirst($transaction->type) }}</div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        @if($asset->operations()->count() === 0 && $asset->financialTransactions()->count() === 0)
                        <p class="text-gray-500">No recent activity for this asset.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
