<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Application Settings') }}
            </h2>
            <a href="{{ route('custom-fields.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Manage Custom Fields
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('settings.update') }}">
                @csrf

                @foreach($settings as $group => $groupSettings)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">{{ ucfirst($group) }} Settings</h3>
                            <form method="POST" action="{{ route('settings.reset') }}" class="inline">
                                @csrf
                                <input type="hidden" name="group" value="{{ $group }}">
                                <button type="submit" 
                                        class="text-sm bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-1 px-3 rounded"
                                        onclick="return confirm('Are you sure you want to reset {{ $group }} settings to defaults?')">
                                    Reset to Defaults
                                </button>
                            </form>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($groupSettings as $setting)
                            <div>
                                <label for="setting_{{ $setting->key }}" class="block text-sm font-medium text-gray-700">
                                    {{ $setting->label }}
                                </label>
                                
                                @if($setting->type === 'boolean')
                                <div class="mt-1">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" 
                                               id="setting_{{ $setting->key }}" 
                                               name="settings[{{ $setting->key }}]" 
                                               value="1"
                                               {{ $setting->typed_value ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-600">Enable</span>
                                    </label>
                                </div>
                                
                                @elseif($setting->type === 'integer')
                                <input type="number" 
                                       id="setting_{{ $setting->key }}" 
                                       name="settings[{{ $setting->key }}]" 
                                       value="{{ $setting->value }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                
                                @elseif($setting->type === 'select')
                                <select id="setting_{{ $setting->key }}" 
                                        name="settings[{{ $setting->key }}]"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @if($setting->key === 'currency')
                                    <option value="USD" {{ $setting->value === 'USD' ? 'selected' : '' }}>USD</option>
                                    <option value="EUR" {{ $setting->value === 'EUR' ? 'selected' : '' }}>EUR</option>
                                    <option value="GBP" {{ $setting->value === 'GBP' ? 'selected' : '' }}>GBP</option>
                                    <option value="CAD" {{ $setting->value === 'CAD' ? 'selected' : '' }}>CAD</option>
                                    @elseif($setting->key === 'timezone')
                                    <option value="America/New_York" {{ $setting->value === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                    <option value="America/Chicago" {{ $setting->value === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                    <option value="America/Denver" {{ $setting->value === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                    <option value="America/Los_Angeles" {{ $setting->value === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                    <option value="UTC" {{ $setting->value === 'UTC' ? 'selected' : '' }}>UTC</option>
                                    @elseif($setting->key === 'date_format')
                                    <option value="Y-m-d" {{ $setting->value === 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                    <option value="m/d/Y" {{ $setting->value === 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                    <option value="d/m/Y" {{ $setting->value === 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                    <option value="M j, Y" {{ $setting->value === 'M j, Y' ? 'selected' : '' }}>Month DD, YYYY</option>
                                    @endif
                                </select>
                                
                                @else
                                <input type="text" 
                                       id="setting_{{ $setting->key }}" 
                                       name="settings[{{ $setting->key }}]" 
                                       value="{{ $setting->value }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @endif
                                
                                @if($setting->description)
                                <p class="mt-1 text-sm text-gray-500">{{ $setting->description }}</p>
                                @endif
                                
                                @error('settings.' . $setting->key)
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endforeach

                <!-- Submit Button -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-end space-x-3">
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Settings Information -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Settings Information</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li><strong>General Settings:</strong> Configure basic application preferences like name, currency, and date formats.</li>
                                <li><strong>Financial Settings:</strong> Set thresholds and automation rules for financial operations.</li>
                                <li><strong>Operations Settings:</strong> Configure maintenance schedules and operation workflows.</li>
                                <li><strong>Custom Fields:</strong> Create additional fields for users, locations, assets, and operations to capture specific business data.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a href="{{ route('custom-fields.index') }}" 
                           class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                            <svg class="h-8 w-8 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            <div>
                                <div class="font-medium text-gray-900">Manage Custom Fields</div>
                                <div class="text-sm text-gray-600">Add or modify custom fields for entities</div>
                            </div>
                        </a>
                        
                        <a href="{{ route('users.index') }}" 
                           class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                            <svg class="h-8 w-8 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <div>
                                <div class="font-medium text-gray-900">User Management</div>
                                <div class="text-sm text-gray-600">Manage user accounts and permissions</div>
                            </div>
                        </a>
                        
                        <a href="{{ route('financial.reports') }}" 
                           class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                            <svg class="h-8 w-8 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <div>
                                <div class="font-medium text-gray-900">Financial Reports</div>
                                <div class="text-sm text-gray-600">View financial analytics and reports</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
