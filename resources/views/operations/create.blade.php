<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create Operation') }}
            </h2>
            <a href="{{ route('operations.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Operations
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('operations.store') }}">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Location -->
                            <div>
                                <label for="location_id" class="block text-sm font-medium text-gray-700">Location *</label>
                                <select id="location_id" name="location_id" required 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Select Location</option>
                                    @foreach($locations as $location)
                                    <option value="{{ $location->id }}" {{ old('location_id') == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                    @endforeach
                                </select>
                                @error('location_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Asset -->
                            <div>
                                <label for="asset_id" class="block text-sm font-medium text-gray-700">Asset (Optional)</label>
                                <select id="asset_id" name="asset_id" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Select Asset</option>
                                    @foreach($assets as $asset)
                                    <option value="{{ $asset->id }}" {{ old('asset_id') == $asset->id ? 'selected' : '' }}>
                                        {{ $asset->name }} ({{ $asset->location->name }})
                                    </option>
                                    @endforeach
                                </select>
                                @error('asset_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Operation Type -->
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700">Operation Type *</label>
                                <select id="type" name="type" required 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Select Type</option>
                                    <option value="maintenance" {{ old('type') === 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                    <option value="repair" {{ old('type') === 'repair' ? 'selected' : '' }}>Repair</option>
                                    <option value="collection" {{ old('type') === 'collection' ? 'selected' : '' }}>Collection</option>
                                    <option value="restocking" {{ old('type') === 'restocking' ? 'selected' : '' }}>Restocking</option>
                                    <option value="inspection" {{ old('type') === 'inspection' ? 'selected' : '' }}>Inspection</option>
                                    <option value="cleaning" {{ old('type') === 'cleaning' ? 'selected' : '' }}>Cleaning</option>
                                    <option value="relocation" {{ old('type') === 'relocation' ? 'selected' : '' }}>Relocation</option>
                                    <option value="other" {{ old('type') === 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Priority -->
                            <div>
                                <label for="priority" class="block text-sm font-medium text-gray-700">Priority *</label>
                                <select id="priority" name="priority" required 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="low" {{ old('priority', 'medium') === 'low' ? 'selected' : '' }}>Low</option>
                                    <option value="medium" {{ old('priority', 'medium') === 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="high" {{ old('priority') === 'high' ? 'selected' : '' }}>High</option>
                                    <option value="urgent" {{ old('priority') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                </select>
                                @error('priority')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Title -->
                            <div class="md:col-span-2">
                                <label for="title" class="block text-sm font-medium text-gray-700">Operation Title *</label>
                                <input type="text" id="title" name="title" value="{{ old('title') }}" required
                                       placeholder="e.g., Monthly maintenance check, Cash collection, Repair coin mechanism"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Scheduled Date -->
                            <div>
                                <label for="scheduled_at" class="block text-sm font-medium text-gray-700">Scheduled Date</label>
                                <input type="datetime-local" id="scheduled_at" name="scheduled_at" 
                                       value="{{ old('scheduled_at') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('scheduled_at')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Estimated Duration -->
                            <div>
                                <label for="estimated_duration" class="block text-sm font-medium text-gray-700">Estimated Duration (hours)</label>
                                <input type="number" id="estimated_duration" name="estimated_duration" 
                                       value="{{ old('estimated_duration') }}" step="0.5" min="0"
                                       placeholder="e.g., 2.5"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('estimated_duration')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Estimated Cost -->
                            <div>
                                <label for="cost" class="block text-sm font-medium text-gray-700">Estimated Cost</label>
                                <input type="number" id="cost" name="cost" value="{{ old('cost') }}" 
                                       step="0.01" min="0" placeholder="0.00"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('cost')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">Status *</label>
                                <select id="status" name="status" required 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="pending" {{ old('status', 'pending') === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="in_progress" {{ old('status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                    <option value="completed" {{ old('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="cancelled" {{ old('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                                @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-6">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea id="description" name="description" rows="4"
                                      placeholder="Detailed description of the operation, including specific tasks, requirements, or issues to address..."
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('description') }}</textarea>
                            @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mt-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Additional Notes</label>
                            <textarea id="notes" name="notes" rows="3"
                                      placeholder="Any additional notes, special instructions, or requirements..."
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('notes') }}</textarea>
                            @error('notes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Custom Fields -->
                        @if($customFields->count() > 0)
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @foreach($customFields as $field)
                                <div>
                                    <label for="custom_fields_{{ $field->name }}" class="block text-sm font-medium text-gray-700">
                                        {{ $field->label }}
                                        @if($field->is_required) * @endif
                                    </label>
                                    
                                    @if($field->field_type === 'text')
                                    <input type="text" id="custom_fields_{{ $field->name }}" 
                                           name="custom_fields[{{ $field->name }}]" 
                                           value="{{ old('custom_fields.' . $field->name, $field->default_value) }}"
                                           {{ $field->is_required ? 'required' : '' }}
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    
                                    @elseif($field->field_type === 'textarea')
                                    <textarea id="custom_fields_{{ $field->name }}" 
                                              name="custom_fields[{{ $field->name }}]" rows="3"
                                              {{ $field->is_required ? 'required' : '' }}
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('custom_fields.' . $field->name, $field->default_value) }}</textarea>
                                    
                                    @elseif($field->field_type === 'select')
                                    <select id="custom_fields_{{ $field->name }}" 
                                            name="custom_fields[{{ $field->name }}]"
                                            {{ $field->is_required ? 'required' : '' }}
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select option</option>
                                        @if($field->options)
                                        @foreach($field->options as $key => $value)
                                        <option value="{{ $key }}" {{ old('custom_fields.' . $field->name) == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                        @endforeach
                                        @endif
                                    </select>
                                    
                                    @elseif($field->field_type === 'number')
                                    <input type="number" id="custom_fields_{{ $field->name }}" 
                                           name="custom_fields[{{ $field->name }}]" 
                                           value="{{ old('custom_fields.' . $field->name, $field->default_value) }}"
                                           {{ $field->is_required ? 'required' : '' }}
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    
                                    @elseif($field->field_type === 'date')
                                    <input type="date" id="custom_fields_{{ $field->name }}" 
                                           name="custom_fields[{{ $field->name }}]" 
                                           value="{{ old('custom_fields.' . $field->name, $field->default_value) }}"
                                           {{ $field->is_required ? 'required' : '' }}
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    
                                    @elseif($field->field_type === 'boolean')
                                    <div class="mt-1">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" id="custom_fields_{{ $field->name }}" 
                                                   name="custom_fields[{{ $field->name }}]" value="1"
                                                   {{ old('custom_fields.' . $field->name, $field->default_value) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm text-gray-600">Yes</span>
                                        </label>
                                    </div>
                                    @endif
                                    
                                    @if($field->help_text)
                                    <p class="mt-1 text-sm text-gray-500">{{ $field->help_text }}</p>
                                    @endif
                                    
                                    @error('custom_fields.' . $field->name)
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Submit Buttons -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{{ route('operations.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Operation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Filter assets by selected location
        document.getElementById('location_id').addEventListener('change', function() {
            const locationId = this.value;
            const assetSelect = document.getElementById('asset_id');
            const assets = @json($assets);
            
            // Clear current options
            assetSelect.innerHTML = '<option value="">Select Asset</option>';
            
            if (locationId) {
                // Filter assets by location
                const filteredAssets = assets.filter(asset => asset.location_id == locationId);
                
                filteredAssets.forEach(asset => {
                    const option = document.createElement('option');
                    option.value = asset.id;
                    option.textContent = asset.name;
                    assetSelect.appendChild(option);
                });
            }
        });
    </script>
</x-app-layout>
