# VendApp - Vending Machine Management System

A comprehensive Laravel application for managing vending machine businesses with extensible architecture, role-based access control, and asset management capabilities.

## Features

### Core Functionality
- **User Management**: Role-based access control (Admin, Manager, Operator) with extensible user fields
- **Location Management**: Track locations with address, coordinates, contact info, and custom fields
- **Asset Management**: Comprehensive asset tracking with QR codes, pricing, and maintenance history
- **Financial Tracking**: Monitor revenue, expenses, and profit across locations and assets
- **Operations Management**: Log and track maintenance, repairs, collections, and custom operations
- **Settings Panel**: Configurable app-wide settings and custom field management

### Technical Features
- **Extensible Architecture**: Custom fields system for all entities (users, locations, assets, operations)
- **Role-Based Access Control**: Powered by <PERSON><PERSON> Permission
- **Activity Logging**: Track all changes with Spatie Laravel Activitylog
- **QR Code Generation**: Built-in QR code generation for assets
- **Responsive UI**: Clean Blade templates with Tailwind CSS
- **Comprehensive Reporting**: Financial reports and analytics

## Requirements

- PHP 8.2 or higher
- Composer
- Node.js & NPM
- MySQL/MariaDB or PostgreSQL
- Laravel 12.x

## Installation

### 1. <PERSON><PERSON> the Repository
```bash
git clone <repository-url>
cd vendapp
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Configure Database
Edit your `.env` file with your database credentials:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=vendapp
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. Run Migrations and Seeders
```bash
# Run migrations
php artisan migrate

# Seed the database with roles and sample data
php artisan db:seed
```

### 6. Build Assets
```bash
# Build frontend assets
npm run build

# Or for development
npm run dev
```

### 7. Storage Link
```bash
# Create storage link for file uploads
php artisan storage:link
```

### 8. Start the Application
```bash
# Start the development server
php artisan serve
```

The application will be available at `http://localhost:8000`

## Default Users

After running the seeders, you can log in with these default accounts:

- **Admin**: <EMAIL> / password
- **Manager**: <EMAIL> / password  
- **Operator**: <EMAIL> / password

## Architecture Overview

### Models and Relationships

#### Core Models
- **User**: System users with roles and permissions
- **Location**: Physical locations where assets are deployed
- **Asset**: Vending machines or other equipment
- **FinancialTransaction**: Revenue, expenses, and financial records
- **Operation**: Maintenance, repairs, and operational activities

#### Extensibility Models
- **CustomField**: Define custom fields for any entity
- **CustomFieldValue**: Store values for custom fields
- **AppSetting**: Application-wide configuration settings

### Role-Based Permissions

#### Admin Role
- Full system access
- User management
- Settings configuration
- All CRUD operations

#### Manager Role
- Location and asset management
- Financial data access
- Operations oversight
- Limited user viewing

#### Operator Role
- Basic location and asset viewing
- Operation execution
- Financial transaction recording
- Limited access scope

## Database Schema

### Core Tables
- `users` - User accounts with extended fields
- `locations` - Physical locations
- `assets` - Vending machines and equipment
- `financial_transactions` - Financial records
- `operations` - Operational activities

### Permission Tables (Spatie)
- `roles` - User roles
- `permissions` - System permissions
- `model_has_roles` - User-role assignments
- `role_has_permissions` - Role-permission assignments

### Extensibility Tables
- `custom_fields` - Field definitions
- `custom_field_values` - Field values
- `app_settings` - Application settings

### Activity Logging
- `activity_log` - Change tracking

## Key Features Explained

### Custom Fields System
The application includes a powerful custom fields system that allows you to extend any entity:

```php
// Define a custom field
CustomField::create([
    'name' => 'maintenance_contract',
    'label' => 'Maintenance Contract Number',
    'entity_type' => 'asset',
    'field_type' => 'text',
    'is_required' => true
]);
```

### QR Code Integration
Assets automatically generate QR codes for easy identification:

```php
// Generate QR code for an asset
$qrCode = QrCode::size(300)->generate($asset->qr_code);
```

### Financial Reporting
Built-in financial tracking with automatic calculations:

```php
// Get location profit
$profit = $location->total_revenue - $location->total_expenses;

// Monthly performance
$monthlyRevenue = $location->financialTransactions()
    ->where('type', 'revenue')
    ->whereMonth('transaction_date', now()->month)
    ->sum('amount');
```

## Extending the Application

### Adding New Entity Types
1. Create migration for the new entity
2. Create model with relationships
3. Add custom field support
4. Create controller with CRUD operations
5. Add routes and views
6. Update navigation

### Adding Custom Field Types
1. Update the `field_type` enum in custom_fields migration
2. Add validation logic in `CustomField::getValidationRulesArray()`
3. Add formatting logic in `CustomField::formatValue()`
4. Update form components in views

### Adding New Roles
1. Create role in seeder or admin panel
2. Define permissions for the role
3. Update middleware and policies
4. Add role-specific navigation items

## API Documentation

The application is designed primarily for web interface, but controllers can be extended to support API endpoints. Key endpoints would include:

- `GET /api/locations` - List locations
- `GET /api/assets` - List assets
- `POST /api/operations` - Create operations
- `GET /api/financial-transactions` - Financial data

## Testing

Run the test suite:

```bash
# Run all tests
php artisan test

# Run specific test
php artisan test --filter UserTest
```

## Deployment

### Production Setup
1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false`
3. Configure proper database credentials
4. Set up web server (Apache/Nginx)
5. Configure SSL certificate
6. Set up backup strategy
7. Configure monitoring

### Performance Optimization
- Enable OPcache
- Use Redis for caching and sessions
- Optimize database queries
- Implement queue workers for background jobs
- Use CDN for static assets

## Security Considerations

- All routes are protected by authentication
- Role-based access control on all operations
- CSRF protection on all forms
- Input validation and sanitization
- File upload restrictions
- Activity logging for audit trails

## Troubleshooting

### Common Issues

1. **Permission denied errors**: Check file permissions on storage and bootstrap/cache
2. **Database connection errors**: Verify database credentials in `.env`
3. **Asset compilation errors**: Run `npm install` and `npm run build`
4. **Role/permission errors**: Run `php artisan db:seed` to create roles

### Debug Mode
Enable debug mode in development:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments and examples

---

**VendApp** - Making vending machine management simple and efficient.
