# VendApp Setup Guide

This guide will walk you through setting up the VendApp vending machine management system from scratch.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **PHP 8.2+** with extensions: BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML
- **Composer** (PHP dependency manager)
- **Node.js 18+** and **NPM**
- **MySQL 8.0+** or **MariaDB 10.3+**
- **Git**

## Step-by-Step Installation

### 1. Clone and Setup Project

```bash
# Clone the repository
git clone <your-repository-url> vendapp
cd vendapp

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 2. Environment Configuration

```bash
# Copy the environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 3. Database Setup

Create a new MySQL database:

```sql
CREATE DATABASE vendapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

Update your `.env` file with database credentials:

```env
APP_NAME="VendApp - Vending Machine Management"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_URL=http://vendapp.test

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=vendapp
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Optional: Configure mail settings for notifications
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### 4. Database Migration and Seeding

```bash
# Run migrations to create database tables
php artisan migrate

# Seed the database with roles, permissions, and sample data
php artisan db:seed
```

This will create:
- Admin, Manager, and Operator roles with appropriate permissions
- Sample users for each role
- Sample locations and assets
- Basic application settings

### 5. Storage and Assets

```bash
# Create symbolic link for file storage
php artisan storage:link

# Build frontend assets
npm run build

# For development with hot reloading
npm run dev
```

### 6. Start the Application

```bash
# Start the Laravel development server
php artisan serve
```

The application will be available at `http://localhost:8000`

## Default Login Credentials

After seeding, you can log in with these accounts:

| Role | Email | Password | Permissions |
|------|-------|----------|-------------|
| Admin | <EMAIL> | password | Full system access |
| Manager | <EMAIL> | password | Location/asset management, financial data |
| Operator | <EMAIL> | password | Basic operations, limited access |

## Verification Steps

### 1. Test Authentication
- Visit `http://localhost:8000`
- Log in with admin credentials
- Verify dashboard loads with sample data

### 2. Test Core Features
- **Locations**: Navigate to Locations and verify sample locations appear
- **Assets**: Check Assets page for sample vending machines
- **Users**: Verify user management (admin only)
- **QR Codes**: Test QR code generation for assets

### 3. Test Permissions
- Log in as different roles and verify access restrictions
- Managers should not see user management
- Operators should have limited access

## Development Setup

### IDE Configuration

For VS Code, install these extensions:
- PHP Intelephense
- Laravel Blade Snippets
- Tailwind CSS IntelliSense

### Code Quality Tools

```bash
# Install development tools
composer require --dev laravel/pint phpunit/phpunit

# Run code formatting
./vendor/bin/pint

# Run tests
php artisan test
```

### Database Tools

Recommended database management tools:
- **TablePlus** (macOS/Windows)
- **phpMyAdmin** (Web-based)
- **MySQL Workbench** (Cross-platform)

## Customization Guide

### Adding Custom Fields

1. **Via Database Seeder** (recommended for initial setup):

```php
// In database/seeders/CustomFieldSeeder.php
CustomField::create([
    'name' => 'warranty_provider',
    'label' => 'Warranty Provider',
    'entity_type' => 'asset',
    'field_type' => 'text',
    'is_required' => false,
    'help_text' => 'Name of the warranty provider company'
]);
```

2. **Via Admin Interface** (when implemented):
- Navigate to Settings > Custom Fields
- Add new field with appropriate configuration

### Extending User Roles

1. **Add New Role**:

```php
// In a seeder or migration
$role = Role::create(['name' => 'technician']);

// Assign specific permissions
$role->givePermissionTo([
    'view assets',
    'edit assets',
    'perform operations'
]);
```

2. **Update Navigation**:

```blade
{{-- In resources/views/layouts/navigation.blade.php --}}
@role('technician')
<x-nav-link :href="route('maintenance.index')">
    {{ __('Maintenance') }}
</x-nav-link>
@endrole
```

### Adding New Modules

1. **Create Model and Migration**:

```bash
php artisan make:model Inventory -m
php artisan make:controller InventoryController --resource
```

2. **Add Relationships**:

```php
// In Asset model
public function inventory()
{
    return $this->hasMany(Inventory::class);
}
```

3. **Create Views and Routes**:

```php
// In routes/web.php
Route::resource('inventory', InventoryController::class);
```

## Production Deployment

### Environment Configuration

```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Use production database
DB_HOST=your-production-host
DB_DATABASE=your-production-db

# Configure caching
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Configure mail
MAIL_MAILER=smtp
# ... production mail settings
```

### Optimization Commands

```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

### Web Server Configuration

**Nginx Example**:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/vendapp/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Troubleshooting

### Common Issues

1. **"Class not found" errors**:
   ```bash
   composer dump-autoload
   php artisan clear-compiled
   ```

2. **Permission errors**:
   ```bash
   sudo chown -R www-data:www-data storage bootstrap/cache
   sudo chmod -R 775 storage bootstrap/cache
   ```

3. **Database connection errors**:
   - Verify database credentials in `.env`
   - Check if database server is running
   - Ensure database exists

4. **Asset compilation errors**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

### Debug Mode

Enable detailed error reporting:

```env
APP_DEBUG=true
LOG_LEVEL=debug
```

Check logs:
```bash
tail -f storage/logs/laravel.log
```

## Next Steps

1. **Customize the application** for your specific business needs
2. **Add additional modules** as required
3. **Configure backup strategy** for production
4. **Set up monitoring** and alerting
5. **Train users** on the system

## Support

- Check the main README for feature documentation
- Review code comments for implementation details
- Create issues for bugs or feature requests
- Refer to Laravel documentation for framework-specific questions

---

**Happy coding!** 🚀
